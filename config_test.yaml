# FlexProxy 测试配置文件
# 包含全局配置和状态码匹配事件的测试配置

# ============================================================================
# 全局配置 (Global Configuration)
# ============================================================================
global:
  # 是否启用代理服务
  # 值: true/false
  # 说明: 控制整个代理服务的开关状态
  enable: true

  # 代理文件路径
  # 值: 文件路径字符串
  # 说明: 指定包含代理服务器列表的文件路径，支持相对路径和绝对路径
  # 示例: "./proxies.txt", "/etc/flexproxy/proxies.txt"
  proxy_file: "./proxies.txt"

  # 全局封禁IP配置列表
  # 说明: 配置需要全局封禁的IP地址及其封禁时长
  global_banned_ips:
    # IP地址: 需要封禁的IP地址
    # duration: 封禁时长，支持三种格式:
    #   1. 整数(秒数): 如 3600 表示3600秒
    #   2. Go时间格式字符串: 如 "1h30m", "300ms", "2s"
    #   3. 特殊值 "reboot": 重启后解封(永久封禁)
    - ip: "*************"
      duration: 3600  # 封禁1小时 (3600秒)
    - ip: "*********"
      duration: "1h30m"  # 封禁1小时30分钟 (Go duration格式)
    - ip: "***********"
      duration: "reboot"  # 重启后解封(永久封禁)

  # 全局封禁域名配置列表
  # 说明: 配置需要全局封禁的域名及其封禁时长
  banned_domains:
    # domain: 需要封禁的域名 (必须是有效的FQDN格式)
    # duration: 封禁时长，支持三种格式:
    #   1. 整数(秒数): 如 86400 表示86400秒
    #   2. Go时间格式字符串: 如 "24h", "30m", "2h15m"
    #   3. 特殊值 "reboot": 重启后解封(永久封禁)
    - domain: "malicious-site.com"
      duration: 86400  # 封禁24小时 (86400秒)
    - domain: "spam-domain.net"
      duration: "24h"  # 封禁24小时 (Go duration格式)
    - domain: "blocked-forever.com"
      duration: "reboot"  # 重启后解封(永久封禁)

  # 阻止访问的IP地址列表
  # 值: IP地址字符串数组
  # 说明: 直接阻止这些IP地址的所有请求，必须是有效的IP地址格式
  blocked_ips:
    - "***********"
    - "************"
    - "*********"

  # 信任的IP地址列表
  # 值: IP地址字符串数组
  # 说明: 这些IP地址的请求将被优先处理或跳过某些检查
  # 注意: 必须是单个IP地址，不支持CIDR表示法（如***********/24）
  # 如需支持IP段，请列出具体的IP地址
  trusted_ips:
    - "127.0.0.1"      # 本地回环地址IPv4
    - "::1"            # 本地回环地址IPv6
    - "***********"    # 示例：内网网关IP
    - "********"       # 示例：内网IP

  # 排除的URL模式列表
  # 值: 字符串数组
  # 说明: 匹配这些模式的URL将不会通过代理处理
  # 支持正则表达式和通配符模式
  excluded_patterns:
    - "*.local"
    - "localhost:*"
    - "127.0.0.1:*"
    - "*.internal.company.com"

  # 排除范围
  # 值: 字符串
  # 说明: 定义排除规则的应用范围和匹配方式
  # 可选值:
  #   - "extension": 仅匹配文件扩展名
  #   - "domain": 仅匹配域名
  #   - "url": 仅匹配完整URL
  #   - "all": 尝试所有匹配方式（URL、域名、扩展名）
  #   - 其他值: 默认行为，等同于"all"
  # 推荐值: "all" (最全面的匹配方式)
  excluded_scope: "all"

  # 规则优先级
  # 值: 0-100的整数
  # 说明: 定义规则处理的优先级，数值越高优先级越高
  # 0=最低优先级, 100=最高优先级
  rule_priority: 50

  # 默认处理阶段
  # 值: "pre", "post_header", "post_body"
  # 说明: 定义默认的请求处理阶段
  # - pre: 请求前处理
  # - post_header: 响应头处理
  # - post_body: 响应体处理
  default_process_stage: "pre"

  # DNS查找模式
  # 值: "system", "custom", "hybrid"
  # 说明: 定义DNS解析的方式
  # 注意: 代码中实际使用的常量是 "local", "remote", "custom"
  # 但验证标签要求 "system", "custom", "hybrid"，这里使用验证标签支持的值
  # - system: 使用系统DNS (对应代码中的"local")
  # - custom: 使用自定义DNS服务器
  # - hybrid: 混合模式，优先使用自定义DNS，失败时回退到系统DNS
  dns_lookup_mode: "custom"

  # 反向DNS查找
  # 值: "no", "dns", "file:/path/to/hosts.txt", 或直接映射值
  # 说明: 配置反向DNS查找的行为
  # - "no": 不进行反向DNS解析
  # - "dns": 使用系统DNS进行反向解析
  # - "file:/path/to/hosts.txt": 从文件加载IP到域名的映射 (格式: ******* example.com)
  # - 直接映射: "******* example.com, ******* another.com" (逗号分隔多个映射)
  reverse_dns_lookup: "dns"

  # 自定义DNS服务器配置列表
  # 说明: 配置自定义DNS服务器的详细参数
  # ⚠️  重要说明:
  # - server字段在YAML配置中只能使用纯IP地址（验证器限制）
  # - 端口配置通过protocol字段和默认端口处理
  # - HTTPProxyDNS字段支持 "IP:端口" 格式
  custom_dns_servers:
    # 主要DNS服务器 - Cloudflare
    - server: "*******"  # 纯IP地址（验证器要求）
      protocol: "udp"    # 协议: udp, tcp, doh, dot (注意: 验证标签要求 "udp tcp doh dot")
      timeout: 5000      # 超时时间(毫秒): 1000-30000
      priority: 1        # 优先级: 0-100, 数值越小优先级越高
      tags: ["cloudflare", "primary"]

    # 备用DNS服务器 - Google
    - server: "*******"  # 纯IP地址
      protocol: "tcp"    # 使用TCP协议提高可靠性
      timeout: 5000
      priority: 2
      tags: ["google", "backup"]

    # DoH服务器示例 - 注意: 实际代码支持 "https" 和 "doh" 协议
    - server: "*******"  # Cloudflare备用IP
      protocol: "doh"    # DNS over HTTPS
      timeout: 8000      # DoH通常需要更长超时时间
      priority: 3
      tags: ["cloudflare", "doh", "secure"]

    # DoT服务器示例 - DNS over TLS
    - server: "*******"  # 纯IP地址
      protocol: "dot"    # DNS over TLS (使用标准端口853)
      timeout: 6000
      priority: 4
      tags: ["cloudflare", "dot", "secure"]

    # 自定义DNS服务器（企业内网）
    - server: "***********"  # 纯IP地址
      protocol: "udp"
      timeout: 3000
      priority: 5
      tags: ["internal", "enterprise"]

    # 高级DNS服务器配置示例
    - server: "*******"  # Quad9 DNS
      protocol: "udp"
      timeout: 4000
      priority: 6
      tags: ["quad9", "security", "privacy"]

  # HTTP代理DNS设置
  # 值: DNS服务器地址字符串
  # 说明: 指定HTTP代理使用的DNS服务器，支持DoH (DNS over HTTPS)
  #
  # ✅ 支持的格式:
  # 1. 基本格式: "IP地址" (如 "*******") - 使用默认端口53
  # 2. 端口格式: "IP地址:端口" (如 "*******:5353") - 自定义端口
  # 3. DoH格式: "https://域名/path" (如 "https://*******/dns-query")
  # 4. 复杂格式: "IP:端口@协议,参数" (如 "*******:53@udp,timeout=5000")
  #
  # 示例: "*******:53", "*******:5353", "https://cloudflare-dns.com/dns-query"
  # 留空则使用系统默认DNS
  http_proxy_dns: "*******:53"

  # IP轮换模式
  # 值: "random", "sequential", "quality", "smart"
  # 说明: 定义代理IP的选择策略
  # - random: 随机选择
  # - sequential: 顺序选择
  # - quality: 基于质量评分选择
  # - smart: 智能选择（根据触发动作自动决定是否切换代理）
  ip_rotation_mode: "smart"

  # 最小代理池大小
  # 值: 大于等于1的整数
  # 说明: 维护的最小可用代理数量，低于此数量时会自动补充
  min_proxy_pool_size: 10       

  # 最大代理获取尝试次数
  # 值: 1-10的整数
  # 说明: 获取新代理时的最大尝试次数
  max_proxy_fetch_attempts: 3

  # DNS缓存TTL (生存时间)
  # 值: 大于等于0的整数 (秒)
  # 说明: DNS查询结果的缓存时间，0表示不缓存
  dns_cache_ttl: 300

  # 禁用DNS缓存
  # 值: true/false
  # 说明: 是否完全禁用DNS缓存功能
  dns_no_cache: false

  # IP版本优先级
  # 值: "ipv4", "ipv6", "dual"
  # 说明: 定义IP协议版本的使用优先级
  # - ipv4: 优先使用IPv4
  # - ipv6: 优先使用IPv6
  # - dual: 同时支持IPv4和IPv6
  ip_version_priority: "ipv4"

  # 默认DNS超时时间
  # 值: 1000-30000的整数 (毫秒)
  # 说明: DNS查询的默认超时时间
  default_dns_timeout: 5000

  # 重试代理复用策略
  # 值: "allow", "deny", "cooldown"
  # 说明: 定义失败后重试时是否可以复用相同的代理
  # - allow: 允许立即复用
  # - deny: 禁止复用
  # - cooldown: 冷却时间后可复用
  retry_proxy_reuse_policy: "cooldown"

  # 重试代理冷却时间
  # 值: 大于等于0的整数 (秒)
  # 说明: 代理失败后的冷却时间，在此期间不会被重新使用
  retry_proxy_cooldown_time: 60

  # 重试代理全局跟踪
  # 值: true/false
  # 说明: 是否在全局范围内跟踪代理的重试状态
  # true: 全局跟踪，所有请求共享代理状态
  # false: 独立跟踪，每个请求独立管理代理状态
  retry_proxy_global_tracking: true

# ================================
# 动作序列配置
# ================================
# 定义可重用的动作序列，供事件触发时执行
#
# ⚠️  重要架构说明：
# 当前FlexProxy存在两套动作系统，部分动作类型可能无法正常工作：
#
# ✅ 完全支持的动作类型（有Executor实现）：
#    log, banip, ban_domain, block_request, modify_request,
#    modify_response, cache_response, script
#
# ⚠️  部分支持的动作类型（仅有Action接口实现）：
#    retry_same, retry, banipdomain, save_to_pool, cache,
#    request_url, null_response, bypass_proxy
#
# ❌ 验证器不支持的动作类型（会被config_validator.go拒绝）：
#    alert, block, redirect, rate_limit, webhook, email, slack, discord, custom
#
# 建议：优先使用"完全支持"的动作类型以确保功能正常
actions:
  # 状态码处理动作序列 - 完整动作类型示例
  status_handler_complete:
    sequence:
      # ✅ 完全支持的动作类型

      # 1. log - 记录日志动作
      - type: "log"
        message: "检测到状态码异常，开始执行处理流程"
        level: "warn"  # 可选值: debug, info, warn, error

      # 2. banip - 全局封禁IP动作
      - type: "banip"
        duration: "300s"  # 封禁5分钟，支持Go duration格式

      # 3. ban_domain - 封禁域名动作
      - type: "ban_domain"
        domain: "example.com"
        duration: "1h"  # 封禁1小时

      # 4. block_request - 阻止请求动作
      - type: "block_request"
        reason: "状态码异常，阻止请求"
        status_code: 403  # 返回的HTTP状态码

      # 5. modify_request - 修改请求动作
      - type: "modify_request"
        headers:
          "User-Agent": "FlexProxy/1.0"
          "X-Forwarded-For": "127.0.0.1"
        remove_headers: ["X-Real-IP"]

      # 6. modify_response - 修改响应动作
      - type: "modify_response"
        headers:
          "X-Proxy-Status": "Modified"
          "Cache-Control": "no-cache"
        remove_headers: ["Server"]
        body_replace:
          "error": "success"
          "failed": "completed"

      # 7. cache_response - 缓存响应动作
      - type: "cache_response"
        key: "status_response_cache"
        ttl: "30m"  # 缓存30分钟

      # 8. script - 执行脚本动作
      - type: "script"
        script: "console.log('状态码处理脚本执行');"
        engine: "javascript"  # 脚本引擎类型

  # 重试处理动作序列 - 部分支持的动作类型
  retry_handler:
    sequence:
      # ⚠️  部分支持的动作类型

      # 9. retry_same - 使用相同IP重试
      - type: "retry_same"
        retry_count: 2
        delay: "1s"  # 重试延迟

      # 10. retry - 使用新IP重试
      - type: "retry"
        retry_count: 3
        delay: "2s"

      # 11. banipdomain - 针对域名封禁IP
      - type: "banipdomain"
        duration: "10m"
        scope: "domain"  # 可选值: url, domain, global

      # 12. save_to_pool - 保存到代理池
      - type: "save_to_pool"
        pool_name: "backup_pool"
        quality_score: 80

      # 13. cache - 缓存动作
      - type: "cache"
        duration: "1h"
        max_use_count: 100
        cache_scope: "domain"  # 可选值: global, domain, url
        custom_key: "custom_cache_key"
        ignore_params: false

      # 14. request_url - 请求指定URL动作
      - type: "request_url"
        url: "https://api.example.com/notify"
        method: "POST"
        body: '{"status": "error", "message": "代理异常"}'
        body_type: "json"  # 可选值: json, form, raw, copy
        headers: "Content-Type:application/json,Authorization:Bearer token123"
        timeout_ms: 5000
        proxy_option: "new"  # 可选值: current, new, none, quality:<tier>
        follow_redirect: true
        max_redirects: 5
        validate_ssl: true

      # 15. null_response - 返回空响应
      - type: "null_response"
        status_code: 204
        headers:
          "Content-Length": "0"

      # 16. bypass_proxy - 绕过代理直接连接
      - type: "bypass_proxy"
        timeout_ms: 10000

  # 状态码事件专用动作序列
  log_success:
    sequence:
      - type: "log"
        message: "HTTP 200 成功响应已接收"
        level: "info"

  cache_success_response:
    sequence:
      - type: "cache_response"
        key: "success_response"
        ttl: "10m"

  log_client_error:
    sequence:
      - type: "log"
        message: "检测到HTTP 4xx客户端错误"
        level: "warn"

  retry_with_new_proxy:
    sequence:
      - type: "retry"
        retry_count: 2
        delay: "1s"

  log_not_found:
    sequence:
      - type: "log"
        message: "HTTP 404 Not Found - 资源未找到"
        level: "warn"

  retry_same_proxy:
    sequence:
      - type: "retry_same"
        retry_count: 1
        delay: "500ms"

  fallback_request:
    sequence:
      - type: "request_url"
        url: "https://fallback.example.com/api"
        method: "GET"
        timeout_ms: 3000
        proxy_option: "current"

  ban_current_ip:
    sequence:
      - type: "banip"
        duration: "5m"
      - type: "log"
        message: "由于429限流，已封禁当前IP 5分钟"
        level: "warn"

  retry_with_delay:
    sequence:
      - type: "retry"
        retry_count: 3
        delay: "5s"

  log_server_error:
    sequence:
      - type: "log"
        message: "检测到HTTP 5xx服务器错误"
        level: "error"

  retry_with_backoff:
    sequence:
      - type: "retry"
        retry_count: 3
        delay: "2s"

  ban_proxy_temporarily:
    sequence:
      - type: "banipdomain"
        duration: "2m"
        scope: "domain"
      - type: "log"
        message: "502错误 - 已临时封禁代理2分钟"
        level: "warn"

  switch_to_quality_proxy:
    sequence:
      - type: "retry"
        retry_count: 2
        delay: "1s"

  log_service_unavailable:
    sequence:
      - type: "log"
        message: "HTTP 503 服务不可用"
        level: "error"

  retry_with_exponential_backoff:
    sequence:
      - type: "retry"
        retry_count: 4
        delay: "3s"

  log_redirect:
    sequence:
      - type: "log"
        message: "检测到HTTP重定向响应"
        level: "info"

  cache_redirect_info:
    sequence:
      - type: "cache_response"
        key: "redirect_info"
        ttl: "5m"

  log_unusual_status:
    sequence:
      - type: "log"
        message: "检测到异常状态码响应"
        level: "warn"

  comprehensive_handler:
    sequence:
      - type: "log"
        message: "执行综合状态码处理流程"
        level: "info"
      - type: "cache_response"
        key: "unusual_response"
        ttl: "1m"

  complex_rate_limit_handler:
    sequence:
      - type: "log"
        message: "复杂限流条件触发 - 执行特殊处理"
        level: "error"
      - type: "banip"
        duration: "10m"
      - type: "retry"
        retry_count: 5
        delay: "10s"

  domain_specific_action:
    sequence:
      - type: "log"
        message: "域名特定动作已触发"
        level: "info"
      - type: "modify_request"
        headers:
          "X-Domain-Handler": "active"
      - type: "request_url"
        url: "https://monitoring.example.com/alert"
        method: "POST"
        body: '{"event": "domain_specific_trigger", "timestamp": "{{.timestamp}}"}'
        body_type: "json"
        timeout_ms: 3000

# ================================
# 事件配置
# ================================
# 定义各种触发事件和对应的处理动作
events:
  # HTTP状态码匹配事件 - 成功响应处理
  http_status_200_success:
    name: "HTTP 200 成功响应处理"
    description: "处理HTTP 200状态码响应，记录成功信息并缓存结果"
    enabled: true
    priority: 10
    process_stage: "post_header"  # 在响应头处理阶段触发
    trigger:
      type: "response_status"
      conditions:
        status_codes: [200]  # 匹配HTTP 200状态码
        match_mode: "exact"  # 精确匹配模式
    actions: ["log_success", "cache_success_response"]

  # HTTP状态码匹配事件 - 客户端错误处理
  http_status_4xx_client_error:
    name: "HTTP 4xx 客户端错误处理"
    description: "处理HTTP 4xx客户端错误，包括404、403、429等状态码"
    enabled: true
    priority: 20
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [400, 401, 403, 404, 405, 408, 409, 410, 429]
        match_mode: "any"  # 匹配任意一个状态码
    actions: ["log_client_error", "retry_with_new_proxy"]

  # HTTP状态码匹配事件 - 404 Not Found 特殊处理
  http_status_404_not_found:
    name: "HTTP 404 Not Found 特殊处理"
    description: "专门处理404错误，可能需要特殊的重试策略"
    enabled: true
    priority: 25  # 比通用4xx处理优先级更高
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [404]
        match_mode: "exact"
    actions: ["log_not_found", "retry_same_proxy", "fallback_request"]

  # HTTP状态码匹配事件 - 429 Too Many Requests 限流处理
  http_status_429_rate_limit:
    name: "HTTP 429 限流处理"
    description: "处理429限流响应，封禁当前IP并使用新代理重试"
    enabled: true
    priority: 30  # 高优先级处理
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [429]
        match_mode: "exact"
        headers:  # 可选：检查特定响应头
          "Retry-After": ".*"  # 正则表达式匹配
    actions: ["ban_current_ip", "retry_with_delay"]

  # HTTP状态码匹配事件 - 服务器错误处理
  http_status_5xx_server_error:
    name: "HTTP 5xx 服务器错误处理"
    description: "处理HTTP 5xx服务器错误，包括500、502、503、504等"
    enabled: true
    priority: 40
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [500, 502, 503, 504, 505, 507, 508, 510, 511]
        match_mode: "any"
    actions: ["log_server_error", "retry_with_backoff"]

  # HTTP状态码匹配事件 - 502 Bad Gateway 特殊处理
  http_status_502_bad_gateway:
    name: "HTTP 502 Bad Gateway 特殊处理"
    description: "专门处理502网关错误，可能是代理问题"
    enabled: true
    priority: 45  # 比通用5xx处理优先级更高
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [502]
        match_mode: "exact"
    actions: ["ban_proxy_temporarily", "switch_to_quality_proxy"]

  # HTTP状态码匹配事件 - 503 Service Unavailable 处理
  http_status_503_unavailable:
    name: "HTTP 503 服务不可用处理"
    description: "处理503服务不可用错误，可能需要等待后重试"
    enabled: true
    priority: 45
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [503]
        match_mode: "exact"
    actions: ["log_service_unavailable", "retry_with_exponential_backoff"]

  # HTTP状态码匹配事件 - 重定向处理
  http_status_3xx_redirect:
    name: "HTTP 3xx 重定向处理"
    description: "处理HTTP重定向响应，记录重定向信息"
    enabled: true
    priority: 15
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [301, 302, 303, 307, 308]
        match_mode: "any"
        headers:
          "Location": ".*"  # 必须包含Location头
    actions: ["log_redirect", "cache_redirect_info"]

  # HTTP状态码匹配事件 - 综合状态码范围处理
  http_status_range_comprehensive:
    name: "综合状态码范围处理"
    description: "使用状态码范围匹配的综合处理示例"
    enabled: true
    priority: 5  # 低优先级，作为兜底处理
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_code_ranges:  # 状态码范围匹配
          - min: 100
            max: 199  # 1xx 信息响应
          - min: 600
            max: 999  # 非标准状态码
        match_mode: "range"
    actions: ["log_unusual_status", "comprehensive_handler"]

  # HTTP状态码匹配事件 - 条件组合处理
  http_status_conditional_complex:
    name: "复杂条件组合状态码处理"
    description: "演示复杂条件组合的状态码匹配"
    enabled: true
    priority: 35
    process_stage: "post_header"
    trigger:
      type: "response_status"
      conditions:
        status_codes: [403, 429, 503]
        match_mode: "any"
        headers:
          "X-RateLimit-Remaining": "0"  # 限流剩余为0
          "Server": "nginx.*"  # 服务器为nginx
        body_patterns:  # 响应体模式匹配
          - "rate.?limit"
          - "too.?many.?requests"
        domain_patterns:  # 域名模式匹配
          - "api\\..*\\.com"
          - ".*\\.googleapis\\.com"
    actions: ["complex_rate_limit_handler", "domain_specific_action"]

# ================================
# 完整动作类型参数说明和示例
# ================================
# 以下是FlexProxy支持的所有动作类型的详细参数说明和使用示例

# 📋 动作类型分类说明：
# ✅ 完全支持（有Executor实现）: log, banip, ban_domain, block_request, modify_request, modify_response, cache_response, script
# ⚠️  部分支持（仅Action接口）: retry_same, retry, banipdomain, save_to_pool, cache, request_url, null_response, bypass_proxy
# ❌ 验证器不支持: alert, block, redirect, rate_limit, webhook, email, slack, discord, custom

# 完整动作类型示例配置
complete_action_examples:
  # ========== ✅ 完全支持的动作类型 ==========

  # 1. log - 日志记录动作
  example_log_action:
    sequence:
      - type: "log"
        message: "这是一条日志消息"  # 必需参数：日志内容
        level: "info"  # 可选参数：日志级别 (debug, info, warn, error)

  # 2. banip - 全局IP封禁动作
  example_banip_action:
    sequence:
      - type: "banip"
        ip: "*************"  # 必需参数：要封禁的IP地址
        duration: "1h"  # 可选参数：封禁时长，默认"1h"，支持Go duration格式或"reboot"

  # 3. ban_domain - 域名封禁动作
  example_ban_domain_action:
    sequence:
      - type: "ban_domain"
        domain: "malicious-site.com"  # 必需参数：要封禁的域名
        duration: "24h"  # 可选参数：封禁时长，默认"1h"

  # 4. block_request - 阻止请求动作
  example_block_request_action:
    sequence:
      - type: "block_request"
        reason: "请求被安全策略阻止"  # 可选参数：阻止原因
        status_code: 403  # 可选参数：返回的HTTP状态码
        response_body: "访问被拒绝"  # 可选参数：自定义响应体

  # 5. modify_request - 修改请求动作
  example_modify_request_action:
    sequence:
      - type: "modify_request"
        headers:  # 可选参数：要添加或修改的请求头
          "User-Agent": "FlexProxy/1.0 (Custom Agent)"
          "X-Forwarded-For": "127.0.0.1"
          "Authorization": "Bearer custom-token"
        remove_headers:  # 可选参数：要删除的请求头列表
          - "X-Real-IP"
          - "X-Original-Host"
        url_rewrite:  # 可选参数：URL重写规则
          pattern: "^/api/v1/"
          replacement: "/api/v2/"
        query_params:  # 可选参数：查询参数修改
          add:
            "version": "2.0"
            "client": "flexproxy"
          remove:
            - "debug"
            - "trace"

  # 6. modify_response - 修改响应动作
  example_modify_response_action:
    sequence:
      - type: "modify_response"
        headers:  # 可选参数：要添加或修改的响应头
          "X-Proxy-Status": "Modified by FlexProxy"
          "Cache-Control": "no-cache, no-store"
          "X-Content-Modified": "true"
        remove_headers:  # 可选参数：要删除的响应头列表
          - "Server"
          - "X-Powered-By"
        status_code: 200  # 可选参数：修改响应状态码
        body_replace:  # 可选参数：响应体内容替换
          "error": "success"
          "failed": "completed"
          "unauthorized": "authorized"
        body_inject:  # 可选参数：向响应体注入内容
          position: "end"  # 注入位置：start, end, before:<pattern>, after:<pattern>
          content: "\n<!-- Modified by FlexProxy -->"

  # 7. cache_response - 缓存响应动作
  example_cache_response_action:
    sequence:
      - type: "cache_response"
        key: "custom_cache_key"  # 必需参数：缓存键名
        ttl: "30m"  # 可选参数：缓存生存时间，默认"1h"
        max_size: 1048576  # 可选参数：最大缓存大小（字节）
        compress: true  # 可选参数：是否压缩缓存内容

  # 8. script - 脚本执行动作
  example_script_action:
    sequence:
      - type: "script"
        script: |  # 必需参数：要执行的脚本代码
          console.log('FlexProxy脚本执行开始');
          var result = processRequest(request, response);
          console.log('处理结果:', result);
          return result;
        engine: "javascript"  # 可选参数：脚本引擎类型
        timeout: 5000  # 可选参数：脚本执行超时时间（毫秒）
        context:  # 可选参数：脚本执行上下文变量
          environment: "production"
          version: "1.0"

  # ========== ⚠️  部分支持的动作类型 ==========

  # 9. retry_same - 使用相同IP重试动作
  example_retry_same_action:
    sequence:
      - type: "retry_same"
        retry_count: 3  # 可选参数：重试次数，默认1
        delay: "2s"  # 可选参数：重试延迟，支持Go duration格式
        max_delay: "30s"  # 可选参数：最大延迟时间
        backoff_factor: 2.0  # 可选参数：退避因子（指数退避）

  # 10. retry - 使用新IP重试动作
  example_retry_action:
    sequence:
      - type: "retry"
        retry_count: 5  # 可选参数：重试次数，默认1
        delay: "1s"  # 可选参数：重试延迟
        strategy: "exponential"  # 可选参数：重试策略 (linear, exponential)
        jitter: true  # 可选参数：是否添加随机抖动

  # 11. banipdomain - 针对域名封禁IP动作
  example_banipdomain_action:
    sequence:
      - type: "banipdomain"
        duration: "15m"  # 可选参数：封禁时长，默认"1h"
        scope: "domain"  # 可选参数：封禁范围 (url, domain, global)
        reason: "域名访问异常"  # 可选参数：封禁原因

  # 12. save_to_pool - 保存到代理池动作
  example_save_to_pool_action:
    sequence:
      - type: "save_to_pool"
        pool_name: "high_quality_pool"  # 可选参数：目标代理池名称
        quality_score: 85  # 可选参数：质量评分 (0-100)
        tags:  # 可选参数：代理标签
          - "fast"
          - "stable"
          - "premium"
        metadata:  # 可选参数：附加元数据
          region: "us-east"
          provider: "premium-proxy-service"

  # 13. cache - 缓存动作（增强版）
  example_cache_action:
    sequence:
      - type: "cache"
        duration: "2h"  # 可选参数：缓存持续时间，默认"1h"
        max_use_count: 50  # 可选参数：最大使用次数，默认无限制
        cache_scope: "domain"  # 可选参数：缓存范围 (global, domain, url)
        custom_key: "api_response_cache"  # 可选参数：自定义缓存键
        ignore_params: false  # 可选参数：是否忽略URL参数
        conditions:  # 可选参数：缓存条件
          status_codes: [200, 201, 202]  # 仅缓存特定状态码
          content_types: ["application/json", "text/html"]  # 仅缓存特定内容类型

  # 14. request_url - 请求指定URL动作（完整参数）
  example_request_url_action:
    sequence:
      - type: "request_url"
        url: "https://api.webhook.site/unique-id"  # 必需参数：目标URL
        method: "POST"  # 可选参数：HTTP方法，默认"GET"
        body: |  # 可选参数：请求体内容
          {
            "event": "proxy_event",
            "timestamp": "{{.timestamp}}",
            "proxy_ip": "{{.proxy_ip}}",
            "target_domain": "{{.domain}}",
            "status": "triggered"
          }
        body_type: "json"  # 可选参数：请求体类型 (json, form, raw, copy)
        headers: "Content-Type:application/json,X-API-Key:secret123,User-Agent:FlexProxy/1.0"  # 可选参数：请求头
        timeout_ms: 8000  # 可选参数：请求超时时间（毫秒），默认5000
        proxy_option: "quality:premium"  # 可选参数：代理选项 (current, new, none, quality:<tier>)
        follow_redirect: true  # 可选参数：是否跟随重定向，默认true
        max_redirects: 10  # 可选参数：最大重定向次数，默认5
        validate_ssl: false  # 可选参数：是否验证SSL证书，默认true
        retry_count: 2  # 可选参数：失败重试次数
        expected_status: [200, 201, 202]  # 可选参数：期望的响应状态码

  # 15. null_response - 返回空响应动作
  example_null_response_action:
    sequence:
      - type: "null_response"
        status_code: 204  # 可选参数：响应状态码，默认204
        headers:  # 可选参数：响应头
          "Content-Length": "0"
          "X-Null-Response": "true"
          "Cache-Control": "no-cache"
        reason: "请求被过滤，返回空响应"  # 可选参数：空响应原因

  # 16. bypass_proxy - 绕过代理直接连接动作
  example_bypass_proxy_action:
    sequence:
      - type: "bypass_proxy"
        timeout_ms: 15000  # 可选参数：直连超时时间（毫秒），默认10000
        dns_server: "*******:53"  # 可选参数：直连使用的DNS服务器
        bind_interface: "eth0"  # 可选参数：绑定的网络接口
        local_addr: "*************"  # 可选参数：本地绑定地址
        reason: "目标服务器要求直连访问"  # 可选参数：绕过代理的原因

  # Wire系统服务依赖示例动作
  wire_service_integration_example:
    sequence:
      # 利用CacheService的缓存动作
      - type: "cache_response"
        key: "wire_cache_example"
        ttl: "15m"

      # 利用LogService的日志动作
      - type: "log"
        message: "Wire系统服务集成示例 - 缓存和日志服务协同工作"
        level: "info"

      # 利用ProxyService的IP封禁动作
      - type: "banip"
        duration: "5m"

      # 利用ActionService的脚本执行动作
      - type: "script"
        script: |
          // Wire容器中的服务可以通过上下文访问
          console.log('Wire服务集成脚本执行');
          // 这里可以访问注入的服务实例
          return { status: 'success', message: 'Wire集成完成' };
        engine: "javascript"
        timeout: 3000

# ================================
# Wire系统配置最佳实践
# ================================

# 🏗️ Wire依赖注入配置建议：
#
# 1. 服务初始化顺序：
#    - 基础服务优先：LogService, ConfigService, CacheService
#    - 核心服务其次：DNSService, ProxyService, StrategyManager
#    - 业务服务最后：TriggerService, ActionService
#    - 高级服务按需：MonitoringService, SecurityService等
#
# 2. 配置文件与Wire服务的对应关系：
#    - global配置 → 各服务的Initialize()方法
#    - actions配置 → ActionService.initializeActions()
#    - events配置 → TriggerService.initializeTriggers()
#    - 服务间通信通过Wire注入的接口进行
#
# 3. 服务生命周期管理：
#    - 使用context.Context进行优雅关闭
#    - 服务启动失败时的回滚机制
#    - 配置热重载时的服务重启策略
#
# 4. 错误处理和恢复：
#    - 利用AdvancedConfigService的ErrorRecovery配置
#    - 服务间错误传播和隔离
#    - 断路器模式防止级联失败
#
# 5. 性能优化：
#    - 利用PerformanceService监控服务性能
#    - 合理配置工作池大小和队列长度
#    - 使用ProfilingService进行性能分析

# ================================
# 配置使用说明和最佳实践
# ================================

# 📖 状态码匹配事件使用指南：
#
# 1. 事件优先级：
#    - 数值越高，优先级越高
#    - 建议为特定状态码设置更高优先级（如404、429、502）
#    - 通用范围匹配使用较低优先级作为兜底处理
#
# 2. 处理阶段选择：
#    - pre: 请求前处理（适用于请求修改、预检查）
#    - post_header: 响应头处理（适用于状态码检查、头部分析）
#    - post_body: 响应体处理（适用于内容分析、完整响应处理）
#
# 3. 状态码匹配模式：
#    - exact: 精确匹配单个状态码
#    - any: 匹配列表中任意一个状态码
#    - range: 匹配状态码范围
#    - pattern: 使用正则表达式匹配
#
# 4. 动作序列设计原则：
#    - 优先使用"完全支持"的动作类型确保稳定性
#    - 合理组合多个动作实现复杂逻辑
#    - 注意动作执行顺序，某些动作可能影响后续执行
#    - 添加适当的日志记录便于调试和监控
#
# 5. 性能优化建议：
#    - 避免在高频事件中使用耗时动作（如request_url）
#    - 合理设置缓存TTL和重试次数
#    - 使用条件匹配减少不必要的动作执行
#    - 定期清理过期的封禁规则和缓存数据
#
# 6. 安全注意事项：
#    - 谨慎使用script动作，确保脚本代码安全
#    - 合理设置封禁时长，避免误封正常IP
#    - 保护敏感信息（如API密钥）不要明文写入配置
#    - 定期审查和更新安全策略

# ⚠️  重要提醒：
# - 本配置文件仅用于测试和示例，生产环境请根据实际需求调整
# - 部分动作类型可能需要额外的服务支持（如缓存服务、脚本引擎）
# - 建议在测试环境充分验证配置后再部署到生产环境
# - 监控日志输出以确保配置按预期工作

# � Wire依赖注入系统说明：
# FlexProxy使用Google Wire进行依赖注入管理，相关服务通过Wire容器初始化：
#
# 核心服务依赖关系：
# - ConfigService: 配置管理服务
# - LogService: 日志服务（基于logo库）
# - CacheService: 缓存服务
# - DNSService: DNS解析服务
# - ProxyService: 代理管理服务
# - TriggerService: 触发器服务（处理事件）
# - ActionService: 动作执行服务（处理动作序列）
# - MonitoringService: 监控服务
# - RateLimitingService: 限流服务
# - SecurityService: 安全服务
# - PluginService: 插件服务
# - StrategyManager: 策略管理器（包含smart模式）
#
# 高级服务：
# - TracingService: 链路追踪服务
# - PerformanceService: 性能监控服务
# - DebugService: 调试服务
# - ProfilingService: 性能分析服务
# - AdvancedConfigService: 高级配置管理服务
#
# Wire容器初始化：
# - InitializeContainer(): 使用默认配置初始化容器
# - InitializeContainerWithConfig(config): 使用指定配置初始化容器
#
# 服务生命周期：
# 1. Wire容器创建所有服务实例
# 2. 各服务通过Initialize()方法初始化
# 3. 通过Start()方法启动服务
# 4. 通过Stop()方法停止服务
#
# 配置加载流程：
# 1. ConfigService加载YAML配置文件
# 2. TriggerService根据events配置初始化触发器
# 3. ActionService根据actions配置初始化动作序列
# 4. 各服务根据global配置调整行为参数

# �📚 相关文档和资源：
# - FlexProxy官方文档: https://github.com/your-org/flexproxy
# - Google Wire文档: https://github.com/google/wire
# - 配置验证工具: flexproxy --validate-config config_test.yaml
# - Wire代码生成: go generate ./internal/wire/
# - 性能监控面板: http://localhost:8080/metrics
# - 日志查看命令: tail -f /var/log/flexproxy/flexproxy.log
# - 依赖注入调试: 查看 internal/wire/wire_gen.go 生成的代码
