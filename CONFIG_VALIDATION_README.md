# FlexProxy 配置文件验证机制

## 概述

FlexProxy 现在具备了完善的配置文件验证机制，确保程序启动时配置文件的完整性和有效性。验证失败时，程序会立即终止并返回相应的退出码。

## 验证时机

### 1. 程序启动时验证
- 在 `main.go` → `runner.New()` 中进行早期配置验证
- 使用 `common.ValidateConfigFile()` 函数
- 验证失败时立即终止程序执行

### 2. 配置加载时验证
- 在 `common.LoadConfigFromYAML()` 中进行详细验证
- 使用 `ConfigValidator` 进行结构化验证
- 包括语法验证、业务逻辑验证和深度验证

## 验证范围

### 基础验证
- ✅ 配置文件存在性检查
- ✅ 文件可读性检查
- ✅ YAML 语法正确性
- ✅ 必需字段存在性
- ✅ 字段类型和格式验证

### 业务逻辑验证
- ✅ 代理文件路径有效性
- ✅ 端口配置合理性（1-65535，避免系统保留端口）
- ✅ 认证配置完整性
- ✅ DNS 服务器地址格式
- ✅ 缓存配置参数
- ✅ 监控配置有效性

### 深度验证
- ✅ 代理文件存在性检查
- ✅ 端口冲突检测
- ✅ 配置项相互依赖关系
- ✅ 安全配置强度检查

## 错误处理机制

### 统一错误管理
所有配置验证相关的错误常量都统一定义在 `common/errors/errors.go` 中，包括：

#### 配置验证错误代码 (1015-1099)
- `ErrCodeConfigFieldRequired (1015)`: 配置字段是必需的
- `ErrCodeConfigFieldInvalid (1016)`: 配置字段值无效
- `ErrCodeConfigProxyFileEmpty (1017)`: 代理文件路径不能为空
- `ErrCodeConfigProxyFileNotFound (1018)`: 代理文件不存在
- `ErrCodeConfigPortInvalid (1019)`: 端口配置无效
- `ErrCodeConfigPortReserved (1020)`: 使用系统保留端口
- `ErrCodeConfigAuthTypeEmpty (1021)`: 认证类型不能为空
- `ErrCodeConfigAuthIncomplete (1022)`: 认证配置不完整
- `ErrCodeConfigPasswordWeak (1023)`: 密码强度不足
- `ErrCodeConfigDNSServerEmpty (1024)`: DNS服务器地址不能为空
- `ErrCodeConfigDNSServerInvalid (1025)`: DNS服务器地址格式无效
- `ErrCodeConfigDNSTimeoutInvalid (1026)`: DNS超时配置无效
- `ErrCodeConfigCacheTypeEmpty (1027)`: 缓存类型不能为空
- `ErrCodeConfigCacheSizeInvalid (1028)`: 缓存大小配置无效
- `ErrCodeConfigCacheTTLInvalid (1029)`: 缓存TTL配置无效
- `ErrCodeConfigMonitoringPortInvalid (1030)`: 监控端口配置无效
- `ErrCodeConfigMonitoringPathEmpty (1031)`: 监控路径不能为空
- `ErrCodeConfigServiceDisabled (1032)`: 代理服务未启用

### 退出码说明
- `0`: 正常退出
- `1`: 通用错误
- `2`: 配置错误 (CONFIG/VALIDATION)
- `3`: 文件错误 (FILE)
- `4`: 网络错误 (NETWORK)
- `5`: 代理错误 (PROXY)

### 错误信息格式
```
[错误类型:错误代码] 错误信息: 详细信息
```

示例：
```
[CONFIG:1013] 配置文件验证失败: 文件路径: config.yaml
[FILE:1009] 配置文件不存在: 文件路径: nonexistent.yaml
[VALIDATION:1017] 代理文件路径不能为空: 请在 global.proxy_file 中指定有效的代理文件路径
[VALIDATION:1019] 端口配置无效: 端口必须在1-65535范围内，当前值: 99999
```

## 使用方法

### 1. 正常启动（带配置验证）
```bash
go run main.go -a 127.0.0.1:8080 -f proxies.txt -config config.yaml
```

### 2. 配置验证测试
```bash
go run tools/validate_config.go [config_file]
```

### 3. 配置诊断工具
```bash
go run tools/config_doctor.go [config_file]
```

### 4. 配置修复工具
```bash
go run tools/fix_config.go [config_file]
```

## 验证工具说明

### validate_config.go
- 基础配置验证测试
- 显示配置摘要信息
- 检查关键配置项状态

### config_doctor.go
- 全面的配置诊断工具
- 分步骤检查配置问题
- 提供详细的错误分析和解决方案
- 给出配置优化建议

### fix_config.go
- 自动修复常见配置问题
- 设置合理的默认值
- 验证修复后的配置
- 检查关键依赖

## 配置验证示例

### 成功案例
```
RUNNER: [INFO] 开始验证配置文件: config.yaml
RUNNER: [INFO] 配置文件验证通过
RUNNER: [INFO] 已从 config.yaml 加载规则配置
```

### 失败案例
```
RUNNER: [ERROR] 配置文件验证失败: [CONFIG:1013] 配置文件验证失败
RUNNER: [ERROR] 程序无法启动，请检查配置文件并修复以上错误
MAIN: [ERROR] 程序启动失败: [CONFIG:1013] 配置文件验证失败
MAIN: [ERROR] 程序退出，退出码: 2
```

## 常见配置问题及解决方案

### 1. 代理文件问题
**问题**: `代理文件路径不能为空`
**解决**: 在 `global.proxy_file` 中指定有效的代理文件路径

### 2. 端口配置问题
**问题**: `端口必须在1-65535范围内`
**解决**: 检查 `server.port` 配置，使用1024以上的端口

### 3. 认证配置问题
**问题**: `认证类型不能为空`
**解决**: 在 `security.auth.type` 中指定认证类型 (none, basic, bearer, apikey)

### 4. DNS配置问题
**问题**: `DNS服务器地址格式无效`
**解决**: 检查 `dns_service.servers` 中的服务器地址格式

### 5. 缓存配置问题
**问题**: `缓存类型不能为空`
**解决**: 在 `cache.type` 中指定缓存类型 (memory, redis, file)

## 技术实现

### 核心组件
- `common/errors/errors.go`: 统一错误常量定义
- `common/config_validator.go`: 配置验证器
- `common/config_loader.go`: 配置加载器
- `internal/runner/runner.go`: 启动时验证逻辑
- `main.go`: 错误处理和退出码管理

### 验证流程
1. 文件存在性检查
2. YAML 语法解析
3. 结构化字段验证
4. 业务逻辑验证
5. 深度依赖检查
6. 启动时需求验证

### 错误常量管理
所有配置验证相关的错误都使用预定义的常量：
- 错误类型：`errors.ErrTypeValidation`, `errors.ErrTypeConfig`, `errors.ErrTypeFile`
- 错误代码：`errors.ErrCodeConfigFieldRequired`, `errors.ErrCodeConfigPortInvalid` 等
- 预定义错误：`errors.ErrConfigProxyFileEmpty`, `errors.ErrConfigPortInvalid` 等

这确保了错误信息的一致性和可维护性。

## 扩展性

配置验证机制具有良好的扩展性：
- 可以轻松添加新的验证规则
- 支持自定义验证函数
- 错误信息可以本地化
- 验证工具可以独立使用

## 最佳实践

1. **开发阶段**: 使用 `config_doctor.go` 诊断配置问题
2. **部署阶段**: 使用 `validate_config.go` 验证配置正确性
3. **生产环境**: 依赖程序启动时的自动验证
4. **故障排除**: 使用 `fix_config.go` 快速修复常见问题

## 日志优化

### 重复日志问题修复
在实现过程中发现并修复了以下重复日志问题：

1. **配置验证失败日志重复**
   - 问题：在 `runner.New()` 中重复调用配置验证
   - 修复：移除重复的 `ValidateConfigFile()` 调用，统一使用 `LoadConfigFromYAML()` 内部验证

2. **代理管理器初始化日志重复**
   - 问题：在 `validate()` 函数中重复创建 `ProxyManager`
   - 修复：移除重复的 `validate()` 调用，确保 `ProxyManager` 只创建一次

### 优化后的日志流程

#### 正常启动流程
```
RUNNER: [INFO] 开始验证配置文件: config.yaml
RUNNER: [INFO] 配置文件验证通过
RUNNER: [INFO] 已从 config.yaml 加载规则配置
PROXY_MANAGER: [INFO] 从 xxx 加载了 1 个代理
PROXY_MANAGER: [INFO] 代理质量系统已为 1 个代理初始化/重新初始化
SERVER: [INFO] [PID: xxx] 代理服务器启动 127.0.0.1:8080
```

#### 配置验证失败流程
```
RUNNER: [INFO] 开始验证配置文件: test_specific_errors.yaml
RUNNER: [ERROR] 配置文件验证失败: [CONFIG:1013] 配置验证失败
RUNNER: [ERROR] 程序无法启动，请检查配置文件并修复以上错误
MAIN: [ERROR] 程序退出，退出码: 2
```

通过这套完整的配置验证机制，FlexProxy 能够在启动时及早发现配置问题，避免运行时错误，提高系统的稳定性和可维护性。
