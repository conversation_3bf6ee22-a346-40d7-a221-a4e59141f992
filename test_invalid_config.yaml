# 测试用的无效配置文件
global:
  enable: false  # 禁用服务
  proxy_file: ""  # 空的代理文件路径

server:
  host: "0.0.0.0"
  port: 99999  # 无效端口

security:
  auth:
    type: ""  # 空的认证类型

dns_service:
  enabled: true
  servers:
    primary: ""  # 空的DNS服务器地址
    secondary: "invalid-dns-server"  # 无效的DNS服务器地址
  timeout: ""  # 空的超时配置
  retries: -1  # 负数重试次数

cache:
  type: ""  # 空的缓存类型
  size: -100  # 负数大小
  ttl: ""
  cleanup_interval: ""

monitoring:
  enabled: true
  port: 0  # 无效端口
  path: ""  # 空路径

# 缺少必需的events配置
events: []

# 缺少必需的actions配置  
actions: {}
