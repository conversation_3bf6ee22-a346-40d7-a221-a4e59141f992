# 测试特定错误的配置文件
global:
  enable: false  # 测试 ErrConfigServiceDisabled
  proxy_file: ""  # 测试 ErrConfigProxyFileEmpty

server:
  host: "0.0.0.0"
  port: 8080

security:
  auth:
    type: ""  # 测试 ErrConfigAuthTypeEmpty

dns_service:
  enabled: true
  servers:
    primary: ""  # 测试 ErrConfigDNSServerEmpty
  timeout: "5s"
  retries: 3

cache:
  type: ""  # 测试 ErrConfigCacheTypeEmpty
  size: 1000
  ttl: "3600s"

monitoring:
  enabled: true
  port: 9090
  path: ""  # 测试 ErrConfigMonitoringPathEmpty

events: []
actions: {}
