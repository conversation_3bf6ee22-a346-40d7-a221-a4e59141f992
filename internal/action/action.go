package action

import (
	"context"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// =============================================================================
// Executor接口定义
// =============================================================================

// Executor 定义动作执行器的接口
type Executor interface {
	// Execute 执行动作
	Execute(ctx context.Context, parameters map[string]interface{}) error
	// Validate 验证参数
	Validate(parameters map[string]interface{}) error
	// GetType 获取执行器类型
	GetType() string
	// GetDescription 获取执行器描述
	GetDescription() string
}

// =============================================================================
// 内置执行器实现
// =============================================================================

// LogExecutor 日志执行器
type LogExecutor struct {
	Logger interfaces.LogService
}

func (e *LogExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	message, ok := parameters["message"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少message参数")
	}

	level, _ := parameters["level"].(string)
	if level == "" {
		level = "info"
	}

	switch strings.ToLower(level) {
	case "debug":
		e.Logger.Debug(message)
	case "info":
		e.Logger.Info(message)
	case "warn":
		e.Logger.Warn(message)
	case "error":
		e.Logger.Error(message)
	default:
		e.Logger.Info(message)
	}

	return nil
}

func (e *LogExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["message"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的message参数")
	}
	return nil
}

func (e *LogExecutor) GetType() string {
	return "log"
}

func (e *LogExecutor) GetDescription() string {
	return "记录日志信息"
}

// BanIPExecutor IP封禁执行器
type BanIPExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := "1h"
	if val, ok := parameters["duration"].(string); ok {
		duration = val
	}

	e.Logger.Info("IP封禁动作已执行: duration=%s", duration)
	return nil
}

func (e *BanIPExecutor) Validate(parameters map[string]interface{}) error {
	// duration 参数是可选的，有默认值
	return nil
}

func (e *BanIPExecutor) GetType() string {
	return "banip"
}

func (e *BanIPExecutor) GetDescription() string {
	return "封禁IP地址"
}

// BanDomainExecutor 域名封禁执行器
type BanDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := "1h"
	if val, ok := parameters["duration"].(string); ok {
		duration = val
	}

	scope := "domain"
	if val, ok := parameters["scope"].(string); ok {
		scope = val
	}

	permanent := false
	if val, ok := parameters["permanent"].(bool); ok {
		permanent = val
	}

	e.Logger.Info("域名封禁动作已执行: duration=%s, scope=%s, permanent=%v", duration, scope, permanent)
	return nil
}

func (e *BanDomainExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *BanDomainExecutor) GetType() string {
	return "ban_domain"
}

func (e *BanDomainExecutor) GetDescription() string {
	return "封禁域名"
}

// BlockRequestExecutor 阻止请求执行器
type BlockRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *BlockRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	reason, _ := parameters["reason"].(string)
	if reason == "" {
		reason = "请求被阻止"
	}

	statusCode := 403
	if val, ok := parameters["status_code"]; ok {
		if sc, ok := val.(int); ok {
			statusCode = sc
		}
	}

	e.Logger.Info("阻止请求动作已执行: reason=%s, status_code=%d", reason, statusCode)
	return nil
}

func (e *BlockRequestExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *BlockRequestExecutor) GetType() string {
	return "block_request"
}

func (e *BlockRequestExecutor) GetDescription() string {
	return "阻止请求"
}

// ModifyRequestExecutor 修改请求执行器
type ModifyRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *ModifyRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)

	e.Logger.Info("修改请求动作已执行: headers=%v, remove_headers=%v", headers, removeHeaders)
	return nil
}

func (e *ModifyRequestExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的
	return nil
}

func (e *ModifyRequestExecutor) GetType() string {
	return "modify_request"
}

func (e *ModifyRequestExecutor) GetDescription() string {
	return "修改请求内容"
}

// ModifyResponseExecutor 修改响应执行器
type ModifyResponseExecutor struct {
	Logger interfaces.LogService
}

func (e *ModifyResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	headers, _ := parameters["headers"].(map[string]interface{})
	statusCode, _ := parameters["status_code"].(int)
	body, _ := parameters["body"].(string)

	e.Logger.Info("修改响应动作已执行: headers=%v, status_code=%d, body_length=%d", headers, statusCode, len(body))
	return nil
}

func (e *ModifyResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的
	return nil
}

func (e *ModifyResponseExecutor) GetType() string {
	return "modify_response"
}

func (e *ModifyResponseExecutor) GetDescription() string {
	return "修改响应内容"
}

// CacheResponseExecutor 缓存响应执行器
type CacheResponseExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := 300 // 默认5分钟
	if val, ok := parameters["duration"]; ok {
		switch v := val.(type) {
		case int:
			duration = v
		case float64:
			duration = int(v)
		case string:
			if d, err := strconv.Atoi(v); err == nil {
				duration = d
			}
		}
	}

	key, _ := parameters["key"].(string)
	if key == "" {
		key = "default"
	}

	e.Logger.Info("缓存响应动作已执行: duration=%d, key=%s", duration, key)
	return nil
}

func (e *CacheResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *CacheResponseExecutor) GetType() string {
	return "cache_response"
}

func (e *CacheResponseExecutor) GetDescription() string {
	return "缓存响应内容"
}

// ScriptExecutor 脚本执行器
type ScriptExecutor struct {
	Logger interfaces.LogService
}

func (e *ScriptExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	script, ok := parameters["script"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少script参数")
	}

	language, _ := parameters["language"].(string)
	if language == "" {
		language = "javascript"
	}

	timeout := 30
	if val, ok := parameters["timeout"]; ok {
		switch v := val.(type) {
		case int:
			timeout = v
		case float64:
			timeout = int(v)
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeout = t
			}
		}
	}

	e.Logger.Info("脚本执行动作已执行: language=%s, timeout=%d, script_length=%d", language, timeout, len(script))
	return nil
}

func (e *ScriptExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["script"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的script参数")
	}
	return nil
}

func (e *ScriptExecutor) GetType() string {
	return "script"
}

func (e *ScriptExecutor) GetDescription() string {
	return "执行自定义脚本"
}

// RetrySameExecutor 使用相同IP重试执行器
type RetrySameExecutor struct {
	Logger interfaces.LogService
}

func (e *RetrySameExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	retryCount := 1
	if val, ok := parameters["retry_count"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	} else if val, ok := parameters["attempts"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	}

	delay := "1s"
	if val, ok := parameters["delay"].(string); ok {
		delay = val
	}

	e.Logger.Info("标记使用相同IP重试 %d 次, 延迟: %s", retryCount, delay)
	return nil
}

func (e *RetrySameExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *RetrySameExecutor) GetType() string {
	return "retry_same"
}

func (e *RetrySameExecutor) GetDescription() string {
	return "使用相同IP重试请求"
}

// RetryExecutor 使用新IP重试执行器
type RetryExecutor struct {
	Logger interfaces.LogService
}

func (e *RetryExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	retryCount := 1
	if val, ok := parameters["retry_count"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	} else if val, ok := parameters["attempts"]; ok {
		switch v := val.(type) {
		case int:
			retryCount = v
		case float64:
			retryCount = int(v)
		case string:
			if rc, err := strconv.Atoi(v); err == nil {
				retryCount = rc
			}
		}
	}

	delay := "2s"
	if val, ok := parameters["delay"].(string); ok {
		delay = val
	}

	e.Logger.Info("标记使用新IP重试 %d 次, 延迟: %s", retryCount, delay)
	return nil
}

func (e *RetryExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *RetryExecutor) GetType() string {
	return "retry"
}

func (e *RetryExecutor) GetDescription() string {
	return "使用新IP重试请求"
}

// BanIPDomainExecutor 针对域名封禁IP执行器
type BanIPDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := "reboot"
	if val, ok := parameters["duration"]; ok {
		switch v := val.(type) {
		case string:
			duration = v
		case int:
			duration = strconv.Itoa(v)
		case float64:
			duration = strconv.Itoa(int(v))
		}
	}

	scope := "domain"
	if val, ok := parameters["scope"].(string); ok {
		scope = val
	}

	e.Logger.Info("针对域名封禁IP动作已执行: duration=%s, scope=%s", duration, scope)
	return nil
}

func (e *BanIPDomainExecutor) Validate(parameters map[string]interface{}) error {
	// duration 和 scope 都是可选参数，有默认值
	return nil
}

func (e *BanIPDomainExecutor) GetType() string {
	return "banipdomain"
}

func (e *BanIPDomainExecutor) GetDescription() string {
	return "针对域名封禁IP"
}

// SaveToPoolExecutor 保存到代理池执行器
type SaveToPoolExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *SaveToPoolExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	qualityTier := "auto"
	if val, ok := parameters["quality_tier"].(string); ok {
		qualityTier = val
	}

	domainSpecific := false
	if val, ok := parameters["domain_specific"].(bool); ok {
		domainSpecific = val
	}

	minScore := 70.0
	if val, ok := parameters["min_score"]; ok {
		switch v := val.(type) {
		case float64:
			minScore = v
		case int:
			minScore = float64(v)
		case string:
			if ms, err := strconv.ParseFloat(v, 64); err == nil {
				minScore = ms
			}
		}
	}

	poolName := "default_pool"
	if val, ok := parameters["pool_name"].(string); ok {
		poolName = val
	}

	e.Logger.Info("保存到代理池动作已执行: quality_tier=%s, domain_specific=%v, min_score=%.1f, pool_name=%s", qualityTier, domainSpecific, minScore, poolName)
	return nil
}

func (e *SaveToPoolExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *SaveToPoolExecutor) GetType() string {
	return "save_to_pool"
}

func (e *SaveToPoolExecutor) GetDescription() string {
	return "保存代理到质量池"
}

// CacheExecutor 缓存执行器
type CacheExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	duration := 300000 // 默认5分钟（毫秒）
	if val, ok := parameters["duration"]; ok {
		switch v := val.(type) {
		case float64:
			duration = int(v)
		case int:
			duration = v
		case string:
			if d, err := strconv.Atoi(v); err == nil {
				duration = d
			}
		}
	}

	maxUseCount := 0
	if val, ok := parameters["max_use_count"]; ok {
		switch v := val.(type) {
		case float64:
			maxUseCount = int(v)
		case int:
			maxUseCount = v
		case string:
			if mu, err := strconv.Atoi(v); err == nil {
				maxUseCount = mu
			}
		}
	}

	cacheScope := "url"
	if val, ok := parameters["cache_scope"].(string); ok {
		cacheScope = val
	}

	customKey := ""
	if val, ok := parameters["custom_key"].(string); ok {
		customKey = val
	}

	ignoreParams := false
	if val, ok := parameters["ignore_params"].(bool); ok {
		ignoreParams = val
	}

	e.Logger.Info("缓存动作已执行: duration=%d, max_use_count=%d, cache_scope=%s, custom_key=%s, ignore_params=%v",
		duration, maxUseCount, cacheScope, customKey, ignoreParams)
	return nil
}

func (e *CacheExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *CacheExecutor) GetType() string {
	return "cache"
}

func (e *CacheExecutor) GetDescription() string {
	return "缓存响应内容"
}

// RequestURLExecutor 请求URL执行器
type RequestURLExecutor struct {
	Logger interfaces.LogService
}

func (e *RequestURLExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	url, ok := parameters["url"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少url参数")
	}

	method := "GET"
	if val, ok := parameters["method"].(string); ok {
		method = strings.ToUpper(val)
	}

	timeoutMS := 30000 // 默认30秒
	if val, ok := parameters["timeout_ms"]; ok {
		switch v := val.(type) {
		case float64:
			timeoutMS = int(v)
		case int:
			timeoutMS = v
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeoutMS = t
			}
		}
	}

	followRedirect := true
	if val, ok := parameters["follow_redirect"].(bool); ok {
		followRedirect = val
	}

	e.Logger.Info("请求URL动作已执行: %s %s (timeout: %dms, follow_redirect: %v)", method, url, timeoutMS, followRedirect)
	return nil
}

func (e *RequestURLExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["url"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的url参数")
	}
	return nil
}

func (e *RequestURLExecutor) GetType() string {
	return "request_url"
}

func (e *RequestURLExecutor) GetDescription() string {
	return "向指定URL发送请求"
}

// NullResponseExecutor 空响应执行器
type NullResponseExecutor struct {
	Logger interfaces.LogService
}

func (e *NullResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	statusCode := 200
	if val, ok := parameters["status_code"]; ok {
		switch v := val.(type) {
		case float64:
			statusCode = int(v)
		case int:
			statusCode = v
		case string:
			if sc, err := strconv.Atoi(v); err == nil {
				statusCode = sc
			}
		}
	}

	contentType := "text/plain"
	if val, ok := parameters["content_type"].(string); ok {
		contentType = val
	}

	body := ""
	if val, ok := parameters["body"].(string); ok {
		body = val
	}

	headers := ""
	if val, ok := parameters["headers"].(string); ok {
		headers = val
	}

	e.Logger.Info("空响应动作已执行: status_code=%d, content_type=%s, body_length=%d, headers=%s", statusCode, contentType, len(body), headers)
	return nil
}

func (e *NullResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *NullResponseExecutor) GetType() string {
	return "null_response"
}

func (e *NullResponseExecutor) GetDescription() string {
	return "返回空响应或自定义响应"
}

// BypassProxyExecutor 绕过代理执行器
type BypassProxyExecutor struct {
	Logger interfaces.LogService
}

func (e *BypassProxyExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	timeoutMS := 30000 // 默认30秒
	if val, ok := parameters["timeout_ms"]; ok {
		switch v := val.(type) {
		case float64:
			timeoutMS = int(v)
		case int:
			timeoutMS = v
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeoutMS = t
			}
		}
	} else if val, ok := parameters["timeout"]; ok {
		switch v := val.(type) {
		case float64:
			timeoutMS = int(v)
		case int:
			timeoutMS = v
		case string:
			if t, err := strconv.Atoi(v); err == nil {
				timeoutMS = t
			}
		}
	}

	e.Logger.Info("绕过代理动作已执行: timeout=%dms", timeoutMS)
	return nil
}

func (e *BypassProxyExecutor) Validate(parameters map[string]interface{}) error {
	// timeout 参数是可选的，有默认值
	return nil
}

func (e *BypassProxyExecutor) GetType() string {
	return "bypass_proxy"
}

func (e *BypassProxyExecutor) GetDescription() string {
	return "绕过代理直接连接"
}

// =============================================================================
// 数据结构定义
// =============================================================================

// 模块级别的日志器
var actionLogger = logger.GetActionLogger()

// ActionDefinition 动作定义（用于依赖注入架构）
type ActionDefinition struct {
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	Enabled      bool                   `json:"enabled"`
	Parameters   map[string]interface{} `json:"parameters"`
	Description  string                 `json:"description"`
	Executor     Executor               `json:"-"`
	CreatedAt    time.Time              `json:"created_at"`
	LastExecuted time.Time              `json:"last_executed"`
	ExecuteCount int                    `json:"execute_count"`
	ErrorCount   int                    `json:"error_count"`
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	ActionName string
	Context    context.Context
	Callback   func(error)
	Timestamp  time.Time
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ActionName string
	Success    bool
	Error      error
	Duration   time.Duration
	Timestamp  time.Time
}

// =============================================================================
// ActionManager 动作管理器
// =============================================================================

// ActionManager 动作管理器
type ActionManager struct {
	executors map[string]Executor
	logger    *logger.LoggerAdapter
}

// NewActionManager 创建新的动作管理器
func NewActionManager() *ActionManager {
	am := &ActionManager{
		executors: make(map[string]Executor),
		logger:    logger.GetLoggerAdapter(logger.ModuleAction),
	}

	// 注册内置执行器
	am.registerBuiltinExecutors()

	return am
}

// registerBuiltinExecutors 注册内置执行器
func (am *ActionManager) registerBuiltinExecutors() {
	// 创建LogService实例
	logService := &logServiceAdapter{adapter: am.logger}

	// 注册所有内置执行器
	am.RegisterExecutor(&LogExecutor{Logger: logService})
	am.RegisterExecutor(&BanIPExecutor{Logger: logService})
	am.RegisterExecutor(&BanDomainExecutor{Logger: logService})
	am.RegisterExecutor(&BlockRequestExecutor{Logger: logService})
	am.RegisterExecutor(&ModifyRequestExecutor{Logger: logService})
	am.RegisterExecutor(&ModifyResponseExecutor{Logger: logService})
	am.RegisterExecutor(&CacheResponseExecutor{Logger: logService})
	am.RegisterExecutor(&ScriptExecutor{Logger: logService})
	am.RegisterExecutor(&RetrySameExecutor{Logger: logService})
	am.RegisterExecutor(&RetryExecutor{Logger: logService})
	am.RegisterExecutor(&BanIPDomainExecutor{Logger: logService})
	am.RegisterExecutor(&SaveToPoolExecutor{Logger: logService})
	am.RegisterExecutor(&CacheExecutor{Logger: logService})
	am.RegisterExecutor(&RequestURLExecutor{Logger: logService})
	am.RegisterExecutor(&NullResponseExecutor{Logger: logService})
	am.RegisterExecutor(&BypassProxyExecutor{Logger: logService})
}

// logServiceAdapter 适配器，将LoggerAdapter转换为LogService接口
type logServiceAdapter struct {
	adapter *logger.LoggerAdapter
}

func (lsa *logServiceAdapter) Info(msg string, args ...interface{}) {
	lsa.adapter.Info(msg, args...)
}

func (lsa *logServiceAdapter) Warn(msg string, args ...interface{}) {
	lsa.adapter.Warn(msg, args...)
}

func (lsa *logServiceAdapter) Error(msg string, args ...interface{}) {
	lsa.adapter.Error(msg, args...)
}

func (lsa *logServiceAdapter) Debug(msg string, args ...interface{}) {
	lsa.adapter.Debug(msg, args...)
}

func (lsa *logServiceAdapter) Fatal(msg string, args ...interface{}) {
	lsa.adapter.Fatal(msg, args...)
}

func (lsa *logServiceAdapter) WithTraceID(traceID string) interfaces.LogService {
	return lsa // 简化实现
}

func (lsa *logServiceAdapter) WithFields(fields map[string]interface{}) interfaces.LogService {
	return lsa // 简化实现
}

func (lsa *logServiceAdapter) LogError(err error, msg string, args ...interface{}) {
	if msg != "" {
		lsa.adapter.Error(msg, args...)
	}
	if err != nil {
		lsa.adapter.Error("Error: %v", err)
	}
}

func (lsa *logServiceAdapter) GetLogger() interface{} {
	return lsa.adapter
}

// RegisterExecutor 注册执行器
func (am *ActionManager) RegisterExecutor(executor Executor) {
	am.executors[executor.GetType()] = executor
}

// GetExecutor 获取执行器
func (am *ActionManager) GetExecutor(actionType string) (Executor, bool) {
	executor, exists := am.executors[actionType]
	return executor, exists
}

// ExecuteAction 执行动作
func (am *ActionManager) ExecuteAction(ctx context.Context, actionType string, parameters map[string]interface{}) error {
	executor, exists := am.GetExecutor(actionType)
	if !exists {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionNotFound, "未找到动作执行器: "+actionType)
	}

	// 验证参数
	if err := executor.Validate(parameters); err != nil {
		return err
	}

	// 执行动作
	return executor.Execute(ctx, parameters)
}

// ListExecutors 列出所有已注册的执行器
func (am *ActionManager) ListExecutors() []string {
	var types []string
	for actionType := range am.executors {
		types = append(types, actionType)
	}
	return types
}

// BuildActionFromConfig 从配置构建动作
func (am *ActionManager) BuildActionFromConfig(actionConfig common.ActionConfig) (*ActionDefinition, error) {
	actionType := actionConfig.Type
	if actionType == "" {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "动作配置缺少type字段")
	}

	executor, exists := am.GetExecutor(actionType)
	if !exists {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionNotFound, "未找到动作执行器: "+actionType)
	}

	// 使用ActionConfig的Params作为参数
	parameters := actionConfig.Params
	if parameters == nil {
		parameters = make(map[string]interface{})
	}

	// 验证参数
	if err := executor.Validate(parameters); err != nil {
		return nil, err
	}

	action := &ActionDefinition{
		Name:         actionType,
		Type:         actionType,
		Enabled:      true,
		Parameters:   parameters,
		Description:  executor.GetDescription(),
		Executor:     executor,
		CreatedAt:    time.Now(),
		LastExecuted: time.Time{},
		ExecuteCount: 0,
		ErrorCount:   0,
	}

	return action, nil
}

// Execute 执行动作定义
func (ad *ActionDefinition) Execute(ctx context.Context, req interface{}, resp interface{}, proxyManager interface{}) (*http.Response, error) {
	if !ad.Enabled {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionDisabled, "动作已禁用: "+ad.Name)
	}

	// 更新执行统计
	ad.LastExecuted = time.Now()
	ad.ExecuteCount++

	// 执行动作
	err := ad.Executor.Execute(ctx, ad.Parameters)
	if err != nil {
		ad.ErrorCount++
		return nil, err
	}

	// 对于大多数动作，不返回HTTP响应，让原始响应继续
	return nil, nil
}
