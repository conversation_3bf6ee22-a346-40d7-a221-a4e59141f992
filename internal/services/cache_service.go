// Package services 提供各种服务实现
package services

import (
	"fmt"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// CacheService 提供缓存功能
// cacheService 缓存服务实现
type cacheService struct {
	mu          sync.RWMutex
	dnsCache    map[string]*cacheItem
	regexCache  map[string]interface{}
	proxyPool   []string
	logger      logger.Logger
}

// cacheItem 缓存项
type cacheItem struct {
	value     interface{}
	expireAt  time.Time
}

// NewCacheService 创建新的缓存服务实例
func NewCacheService(log logger.Logger) interfaces.CacheService {
	if log == nil {
		log = logger.GetLogger("cache")
	}
	
	cs := &cacheService{
		dnsCache:   make(map[string]*cacheItem),
		regexCache: make(map[string]interface{}),
		proxyPool:  make([]string, 0),
		logger:     log,
	}
	
	// 启动清理过期缓存的协程
	go cs.cleanupExpiredCache()
	cs.logger.Info("缓存服务已初始化")
	
	return cs
}

// GetDNSCache 从缓存中获取DNS记录
func (cs *cacheService) GetDNSCache(key string) ([]string, bool) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	
	item, exists := cs.dnsCache[key]
	if !exists {
		cs.logger.Debug(fmt.Sprintf("DNS缓存未命中: %s", key))
		return nil, false
	}
	
	// 检查是否过期
	if time.Now().After(item.expireAt) {
		// 异步删除过期项
		go func() {
			cs.mu.Lock()
			delete(cs.dnsCache, key)
			cs.mu.Unlock()
			cs.logger.Debug(fmt.Sprintf("删除过期DNS缓存项: %s", key))
		}()
		return nil, false
	}
	
	// 类型断言为[]string
	if result, ok := item.value.([]string); ok {
		cs.logger.Debug(fmt.Sprintf("DNS缓存命中: %s", key))
		return result, true
	}
	
	// 如果类型不匹配，返回空
	cs.logger.Debug(fmt.Sprintf("DNS缓存类型不匹配: %s", key))
	return nil, false
}

// SetDNSCache 在缓存中设置DNS记录
func (cs *cacheService) SetDNSCache(key string, value []string, ttl int) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	
	if ttl <= 0 {
		ttl = int(constants.DefaultCacheTTL.Seconds())
	}
	
	expireAt := time.Now().Add(time.Duration(ttl) * time.Second)
	cs.dnsCache[key] = &cacheItem{
		value:    value,
		expireAt: expireAt,
	}
	
	cs.logger.Debug(fmt.Sprintf("DNS缓存已设置: %s, TTL: %d秒, 过期时间: %s, 总缓存数: %d", key, ttl, expireAt.Format(constants.TimeFormatDefault), len(cs.dnsCache)))
}

// GetRegexCache 获取正则表达式缓存
func (cs *cacheService) GetRegexCache(pattern string) (interface{}, bool) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	
	compiled, exists := cs.regexCache[pattern]
	if exists {
		cs.logger.Debug(fmt.Sprintf("正则表达式缓存命中: %s", pattern))
	} else {
		cs.logger.Debug(fmt.Sprintf("正则表达式缓存未命中: %s", pattern))
	}
	return compiled, exists
}

// SetRegexCache 设置正则表达式缓存
func (cs *cacheService) SetRegexCache(pattern string, compiled interface{}) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	
	cs.regexCache[pattern] = compiled
	cs.logger.Debug(fmt.Sprintf("正则表达式缓存已设置: %s, 总缓存数: %d", pattern, len(cs.regexCache)))
}

// GetProxyPool 获取代理池
func (cs *cacheService) GetProxyPool() []string {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	
	// 返回副本以避免并发修改
	pool := make([]string, len(cs.proxyPool))
	copy(pool, cs.proxyPool)
	return pool
}

// UpdateProxyPool 更新代理池
func (cs *cacheService) UpdateProxyPool(proxies []string) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	
	oldSize := len(cs.proxyPool)
	cs.proxyPool = make([]string, len(proxies))
	copy(cs.proxyPool, proxies)
	
	cs.logger.Info(fmt.Sprintf("代理池已更新，旧大小: %d, 新大小: %d, 时间: %s", oldSize, len(proxies), time.Now().Format(constants.TimeFormatDefault)))
}

// cleanupExpiredCache 清理过期缓存的协程
func (cs *cacheService) cleanupExpiredCache() {
	cs.logger.Info("缓存清理协程已启动")
	defer cs.logger.Info("缓存清理协程已停止")
	
	ticker := time.NewTicker(constants.DefaultCleanupInterval)
	defer ticker.Stop()
	
	for range ticker.C {
		cs.mu.Lock()
		now := time.Now()
		expiredCount := 0
		totalBefore := len(cs.dnsCache)
		
		for key, item := range cs.dnsCache {
			if now.After(item.expireAt) {
				delete(cs.dnsCache, key)
				expiredCount++
			}
		}
		
		if expiredCount > 0 {
			cs.logger.Info(fmt.Sprintf("清理过期DNS缓存项完成，过期数: %d, 清理前: %d, 清理后: %d, 时间: %s", expiredCount, totalBefore, len(cs.dnsCache), now.Format(constants.TimeFormatDefault)))
		}
		
		cs.mu.Unlock()
	}
}

// GetCacheStats 获取缓存统计信息
func (cs *cacheService) GetCacheStats() map[string]interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	
	stats := map[string]interface{}{
		"dns_cache_size":   len(cs.dnsCache),
		"regex_cache_size": len(cs.regexCache),
		"proxy_pool_size":  len(cs.proxyPool),
		"timestamp":        time.Now().Format(constants.TimeFormatDefault),
	}
	
	cs.logger.Debug(fmt.Sprintf("获取缓存统计信息: DNS缓存大小: %d, 正则缓存大小: %d, 代理池大小: %d", len(cs.dnsCache), len(cs.regexCache), len(cs.proxyPool)))
	return stats
}

// ClearCache 从缓存中删除所有值
func (cs *cacheService) ClearCache() {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	
	dnsCacheSize := len(cs.dnsCache)
	regexCacheSize := len(cs.regexCache)
	
	cs.dnsCache = make(map[string]*cacheItem)
	cs.regexCache = make(map[string]interface{})
	
	cs.logger.Info(fmt.Sprintf("所有缓存已清空，DNS缓存: %d, 正则缓存: %d, 时间: %s", dnsCacheSize, regexCacheSize, time.Now().Format(constants.TimeFormatDefault)))
}

// ClearAllCache 清空所有缓存（接口兼容方法）
func (cs *cacheService) ClearAllCache() {
	cs.ClearCache()
}

// StartCleanupRoutine 启动清理协程（接口兼容方法）
func (cs *cacheService) StartCleanupRoutine() {
	// 清理协程已在NewCacheService中启动，这里只是接口兼容
	cs.logger.Info("缓存清理协程已启动（接口兼容调用）")
}