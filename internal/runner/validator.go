package runner

import (
	"os"
	"path/filepath"
	"strings"

	"github.com/mubeng/mubeng/pkg/flexproxy"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/internal/proxymanager"
	"github.com/mubeng/mubeng/common/errors"
)

// validate user-supplied option values before Runner.
func validate(opt *common.Options) error {
	var err error

	// if hasStdin() {
	// 	tmp, err := os.CreateTemp("", "flexproxy-stdin-*")
	// 	if err != nil {
	// 		return err
	// 	}
	// 	defer tmp.Close()

	// 	data, err := io.ReadAll(os.Stdin)
	// 	if err != nil {
	// 		return err
	// 	}

	// 	if _, err := tmp.Write(data); err != nil {
	// 		return err
	// 	}

	// 	opt.File = tmp.Name()

	// 	defer os.Remove(opt.File)
	// }

	if opt.File == "" {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "未提供代理文件")
	}

	// 只有在服务器模式下才要求配置文件
	if opt.ConfigFile == "" && opt.Address != "" {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "未指定配置文件")
	}
	// if opt.RuleConfig == nil {
	// 	return errors.New("配置文件未加载成功")
	// }

	opt.File, err = filepath.Abs(opt.File)
	if err != nil {
		return err
	}

	// 获取服务器类型
	flexproxy.ServerType = opt.Type
	opt.ProxyManager, err = proxymanager.New(opt.File, opt.RuleConfig)
	if err != nil {
		return err
	}

	if opt.Address != "" && !opt.Check {
		if opt.Auth != "" {
			auth := strings.SplitN(opt.Auth, ":", 2)
			if len(auth) != 2 {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "代理认证格式无效")
			}
		}
	}

	if opt.CC != "" {
		opt.Countries = strings.Split(opt.CC, ",")
	}

	if opt.Output != "" {
		opt.Output, err = filepath.Abs(opt.Output)
		if err != nil {
			return err
		}

		opt.Result, err = os.OpenFile(opt.Output, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)
		if err != nil {
			return err
		}
	}

	return nil
}
