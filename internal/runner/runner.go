package runner

import (
	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/checker"
	"github.com/mubeng/mubeng/internal/daemon"
	"github.com/mubeng/mubeng/internal/server"
)

// 模块级别的日志器
var runnerLogger = logger.GetRunnerLogger()

// New 切换操作，决定是检查还是运行代理服务器。
func New(opt *common.Options) error {
	// 早期配置文件验证（如果指定）
	if opt.ConfigFile != "" {
		runnerLogger.GetRawLogger().Infof("开始验证配置文件: %s", opt.ConfigFile)

		// 执行早期配置验证
		if err := common.ValidateConfigFile(opt.ConfigFile); err != nil {
			runnerLogger.GetRawLogger().Errorf("配置文件验证失败: %v", err)
			runnerLogger.GetRawLogger().Errorf("程序无法启动，请检查配置文件并修复以上错误")
			return err // 立即返回错误，不继续执行
		}

		runnerLogger.GetRawLogger().Infof("配置文件验证通过")

		// 加载配置文件
		cfg, err := common.LoadConfigFromYAML(opt.ConfigFile)
		if err != nil {
			runnerLogger.GetRawLogger().Errorf("配置文件加载失败: %v", err)
			return err // 立即返回错误
		} else {
			opt.RuleConfig = cfg
			runnerLogger.GetRawLogger().Infof("已从 %s 加载规则配置", opt.ConfigFile)
			// 打印加载后的 Global.Enable 状态
			if opt.RuleConfig != nil {
				runnerLogger.GetRawLogger().Debugf("配置文件加载后 opt.RuleConfig.Global.Enable 的值为: %v", opt.RuleConfig.Global.Enable)
				// 如果命令行没有指定代理文件，但配置文件中有指定，则使用配置文件中的值
				if opt.File == "" && opt.RuleConfig.Global.ProxyFile != "" {
					opt.File = opt.RuleConfig.Global.ProxyFile
					runnerLogger.GetRawLogger().Infof("使用配置文件中指定的代理文件: %s", opt.File)
				}

				runnerLogger.GetRawLogger().Infof("最终的代理文件路径: %s", opt.File)

				// 在配置文件加载之后进行验证
				if err := validate(opt); err != nil {
					return err
				}
			} else {
				runnerLogger.Debug("配置文件加载后 opt.RuleConfig 为 nil")
			}
		}
	} else {
		runnerLogger.Info("未指定配置文件，将使用默认行为。")
	}

	// 无论是否有配置文件，都需要进行基本验证以初始化 ProxyManager
	if err := validate(opt); err != nil {
		return err
	}

	// 在传递给 server.Run 之前再次检查
	if opt.RuleConfig != nil {
		runnerLogger.GetRawLogger().Debugf("传递给 server.Run 之前 opt.RuleConfig.Global.Enable 的值为: %v", opt.RuleConfig.Global.Enable)
	} else {
		runnerLogger.Debug("传递给 server.Run 之前 opt.RuleConfig 为 nil")
	}

	if opt.Address != "" {
		if opt.Daemon {
			return daemon.New(opt)
		}

		server.Run(opt)
	} else if opt.Check {
		checker.Do(opt)

		if opt.Output != "" {
			defer opt.Result.Close()
		}
	} else {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "没有可执行的操作")
	}

	return nil
}
