package server

import (
	"context"
	"crypto/tls"

	"github.com/elazarl/goproxy"
	"github.com/henvic/httpretty"
	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/action" // 添加action包导入
	"github.com/mubeng/mubeng/internal/certificate" // 添加证书管理包
	"github.com/mubeng/mubeng/internal/proxygateway"
	"github.com/mubeng/mubeng/internal/trigger" // 添加trigger包导入

	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"

	"github.com/things-go/go-socks5"
	netProxy "golang.org/x/net/proxy"
)



// Run 使用用户定义的监听器运行代理服务器。
//
// 活动日志有2个接收器，特别是标准输出和文件（如果opt.Output不为空）。
// 然后在接收到中断程序的信号时关闭代理服务器。
func Run(opt *common.Options) {
	dump = &httpretty.Logger{
		RequestHeader:  true,
		ResponseHeader: true,
		Colors:         true,
	}
	handler = &Proxy{}
	handler.Options = opt

	// 初始化IP封禁系统
	if opt.RuleConfig != nil && opt.RuleConfig.Global.Enable {
		opt.ProxyManager.InitBanSystem(opt.RuleConfig)
		serverLogger.GetRawLogger().Infof("初始化封禁系统：有 %d 个被阻止的 IP/域，%d 个受信任的 IP/域",
			len(opt.RuleConfig.Global.BlockedIPs),
			len(opt.RuleConfig.Global.TrustedIPs))

		// 添加排除规则日志
		if len(opt.RuleConfig.Global.ExcludedPatterns) > 0 {
			serverLogger.GetRawLogger().Infof("加载了 %d 个排除规则，范围为 '%s'",
				len(opt.RuleConfig.Global.ExcludedPatterns),
				opt.RuleConfig.Global.ExcludedScope)
		}

		// 启动定期清理过期封禁的协程
		ctx := context.Background()
		opt.ProxyManager.StartBanCleaner(ctx)
	}

	// 初始化DNS解析器
	if opt.RuleConfig != nil {
		dnsMode := opt.RuleConfig.Global.DNSLookupMode
		if dnsMode == "" {
			dnsMode = "local" // 默认本地解析
		}

		// 初始化反向DNS解析器
		reverseDNSMode := opt.RuleConfig.Global.ReverseDNSLookup
		if reverseDNSMode != "" && reverseDNSMode != "no" {
			reverseDNSResolver, err := NewReverseDNSResolver(reverseDNSMode, opt.RuleConfig.Global.ReverseDNSLookup)
			if err != nil {
				serverLogger.GetRawLogger().Warnf("初始化反向DNS解析器失败: %v，将不使用反向DNS解析", err)
			} else {
				handler.reverseDNSResolver = reverseDNSResolver
				serverLogger.GetRawLogger().Infof("初始化反向DNS解析器成功，模式: %s", reverseDNSMode)
			}
		}

		// 根据DNS模式初始化解析器
		if dnsMode == "remote" {
			// 获取当前代理地址用于远程DNS解析
			currentProxy, err := handler.rotateProxy()
			if err != nil {
				serverLogger.Warn("代理池为空或无可用代理，暂停DNS请求")
				return
			}
			// 使用HTTPProxyDNS选项（如果配置）
			handler.dnsResolver = NewRemoteDNSResolver(currentProxy, opt.RuleConfig.Global.HTTPProxyDNS)
			serverLogger.GetRawLogger().Infof("使用远程DNS解析模式，代理: %s", currentProxy)
			if opt.RuleConfig.Global.HTTPProxyDNS != "" {
				serverLogger.GetRawLogger().Infof("使用HTTP代理DNS服务器: %s", opt.RuleConfig.Global.HTTPProxyDNS)
			}
		} else {
			// 本地模式下，根据是否配置了自定义DNS服务器决定使用哪种解析器
			if len(opt.RuleConfig.Global.CustomDNSServers) > 0 {
				// 如果配置了自定义DNS服务器，使用CustomDNSResolver (使用第一个)
				firstCustomDNSServer := opt.RuleConfig.Global.CustomDNSServers[0]
				handler.dnsResolver = NewCustomDNSResolver(firstCustomDNSServer.Server) // 假设 NewCustomDNSResolver 接受服务器地址字符串
				serverLogger.GetRawLogger().Infof("使用本地DNS解析模式，自定义DNS服务器: %s (协议: %s)", firstCustomDNSServer.Server, firstCustomDNSServer.Protocol)
			} else {
				// 如果没有配置自定义DNS服务器，使用系统默认解析器
				handler.dnsResolver = &LocalDNSResolver{}
				serverLogger.GetRawLogger().Infof("使用本地DNS解析模式，系统默认解析器")
			}
		}
	}

	// if opt.Watch {
	// 	watcher, err := opt.ProxyManager.Watch()
	// 	if err != nil {
	// 		log.Fatal(err)
	// 	}
	// 	defer watcher.Close()
	// 	go watch(watcher)
	// }
	if opt.Watch {
		watcher, err := opt.ProxyManager.Watch() // 现在期望 *fsnotify.Watcher
		if err != nil {
			serverLogger.Fatal(err.Error())
		}
		// 这里添加对配置文件的监控
		err = watcher.Add(opt.ConfigFile) // 直接在接口值上调用方法
		if err != nil {
			serverLogger.Fatal(err.Error())
		}
		defer watcher.Close() // 直接在接口值上调用方法
		go watch(watcher, opt.ConfigFile)
	}
	switch opt.Type {
	case "http":
		RunHTTPProxyServer(opt)
	case "socks5":
		RunSocks5ProxyServer(opt)
	}

}

// RunHTTPProxyServer 运行HTTP代理服务器，初始化触发器和动作管理器
func RunHTTPProxyServer(opt *common.Options) {
	// 初始化证书管理系统
	certConfig := &certificate.Config{
		CertDir:     "./certs",
		AutoInstall: true,
	}

	certLogger := logger.GetLogger("CERTIFICATE")
	certManager, err := certificate.NewCertificateManager(certConfig, certLogger)
	if err != nil {
		serverLogger.Error("初始化证书管理器失败: " + err.Error())
		// 继续启动，但HTTPS可能有问题
	} else {
		// 检查证书安装状态
		checker := certificate.NewStartupChecker(certManager, certLogger, true)
		if err := checker.CheckAndSetup(); err != nil {
			serverLogger.Warn("证书设置过程中出现问题: " + err.Error())
		}
	}

	handler.HTTPProxy = goproxy.NewProxyHttpServer()
	handler.HTTPProxy.AllowHTTP2 = true

	// 如果证书管理器初始化成功，设置HTTPS处理
	if certManager != nil {
		// 设置HTTPS拦截，使用动态证书
		handler.HTTPProxy.OnRequest().HandleConnect(goproxy.AlwaysMitm)

		// 设置动态证书生成回调
		handler.HTTPProxy.OnRequest().DoFunc(func(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
			// 存储证书管理器到上下文中，供后续使用
			ctx.UserData = certManager
			return req, nil
		})

		// 为goproxy设置TLS配置
		handler.HTTPProxy.Tr.TLSClientConfig = &tls.Config{
			InsecureSkipVerify: true, // 对上游服务器跳过验证
		}

		// 设置MITM TLS配置 - 使用简化的方式
		handler.HTTPProxy.OnRequest().HandleConnect(goproxy.FuncHttpsHandler(func(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string) {
			// 为每个HTTPS连接动态生成证书
			cert, err := certManager.GetCertificate(host)
			if err != nil {
				serverLogger.Warn("为主机 " + host + " 生成证书失败: " + err.Error())
				return goproxy.OkConnect, host
			}

			// 创建TLS配置函数
			tlsConfigFunc := func(host string, ctx *goproxy.ProxyCtx) (*tls.Config, error) {
				return &tls.Config{
					Certificates: []tls.Certificate{*cert},
					ServerName:   host,
				}, nil
			}

			return &goproxy.ConnectAction{
				Action:    goproxy.ConnectMitm,
				TLSConfig: tlsConfigFunc,
			}, host
		}))

		serverLogger.Info("✅ HTTPS证书管理已启用")
	} else {
		serverLogger.Warn("⚠️  证书管理器未初始化，HTTPS代理可能显示证书警告")
	}

	handler.HTTPProxy.OnRequest().DoFunc(handler.onRequest)
	handler.HTTPProxy.OnRequest().HandleConnectFunc(handler.onConnect)
	handler.HTTPProxy.OnResponse().DoFunc(handler.onResponse)
	handler.HTTPProxy.NonproxyHandler = http.HandlerFunc(nonProxy)
	handler.Gateways = make(map[string]*proxygateway.ProxyGateway)

	// 初始化触发器和动作管理器
	if opt.RuleConfig != nil && opt.RuleConfig.Global.Enable {
		handler.triggerManager = trigger.NewTriggerManager(opt.RuleConfig)
		handler.actionManager = action.NewActionManager() // 注意：NewActionManager 现在不接受参数
		serverLogger.GetRawLogger().Infof("初始化触发器系统，包含 %d 种触发器类型", len(opt.RuleConfig.Events))

		// 初始化全局代理使用跟踪系统
		if opt.RuleConfig.Global.RetryProxyGlobalTracking {
			cooldownTime := 300 // 默认5分钟
			if opt.RuleConfig.Global.RetryProxyCooldownTime > 0 {
				cooldownTime = opt.RuleConfig.Global.RetryProxyCooldownTime
			}
			InitGlobalProxyUsage(true, cooldownTime)
			serverLogger.GetRawLogger().Infof("初始化全局代理使用跟踪系统，冷却时间: %d 秒", cooldownTime)
		}
	}

	server = &http.Server{
		Addr:    opt.Address,
		Handler: handler.HTTPProxy,
	}

	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt)
	go interrupt(stop)

	// 检查 ProxyManager 是否已初始化
	if opt.ProxyManager != nil {
		serverLogger.GetRawLogger().Infof("已加载 %d 个代理", opt.ProxyManager.Count())
	} else {
		serverLogger.GetRawLogger().Warn("ProxyManager 未初始化")
	}

	serverLogger.GetRawLogger().Infof("[PID: %d] 代理服务器启动 %s", os.Getpid(), opt.Address)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		serverLogger.Fatal(err.Error())
	}
}

func RunSocks5ProxyServer(opt *common.Options) {
	// 确保 handler.Options 被正确初始化，使 rotateProxy 能读取 IP 轮换模式
	handler.Options = opt

	socksOpts := []socks5.Option{
		// socks5.WithLogger(logger.ServerDebugf), // 注释掉不兼容的日志器
		socks5.WithDial(func(ctx context.Context, net_, addr string) (net.Conn, error) {
			proxy, err := handler.rotateProxy()
			if err != nil {
				serverLogger.Warn("代理池为空或无可用代理，暂停请求")
				return nil, err
			}
			parse, err := url.Parse(proxy)
			if err != nil {
				return nil, err
			}
			if parse.Host == "" {
				return nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "socks5 地址无效")
			}
			password, _ := parse.User.Password()
			auth := &netProxy.Auth{
				User:     parse.User.Username(),
				Password: password,
			}
			dialer, err := netProxy.SOCKS5("tcp", parse.Host, auth, netProxy.Direct)
			if err != nil {
				return nil, err
			}
			serverLogger.GetRawLogger().Debugf("代理服务器: %v 目标地址: %v", parse.Host, addr)
			return dialer.Dial(net_, addr)
		}),
	}
	if opt.Auth != "" {
		splitN := strings.SplitN(opt.Auth, ":", 2)
		credentials := socks5.StaticCredentials(map[string]string{splitN[0]: splitN[1]})
		socksOpts = append(socksOpts, socks5.WithCredential(credentials))
	}
	// 创建 socks5 服务器
	socks5Server := socks5.NewServer(socksOpts...)
	serverLogger.GetRawLogger().Infof("已加载 %d 个代理", opt.ProxyManager.Count())
	serverLogger.GetRawLogger().Infof("[PID: %d] 代理服务器启动 %s", os.Getpid(), opt.Address)
	if opt.Auth != "" {
		serverLogger.GetRawLogger().Infof("socks url: socks5://%v@%v", opt.Auth, opt.Address)
	} else {
		serverLogger.GetRawLogger().Infof("socks url: socks5://%v", opt.Address)
	}
	// 启动 socks5 服务器
	if err := socks5Server.ListenAndServe("tcp", opt.Address); err != nil {
		serverLogger.Fatal(err.Error())
	}
}
