// Package errors 提供错误处理功能
package errors

import (
	"fmt"
	"runtime"
	"time"
)

// ErrorType 错误类型枚举
type ErrorType string

// String 返回错误类型字符串
func (e ErrorType) String() string {
	return string(e)
}

const (
	// 错误类型
	ErrTypeSystem         ErrorType = "SYSTEM"
	ErrTypeNetwork        ErrorType = "NETWORK"
	ErrTypeProxy          ErrorType = "PROXY"
	ErrTypeProxyManager   ErrorType = "PROXY_MANAGER"
	ErrTypeTrigger        ErrorType = "TRIGGER"
	ErrTypeAction         ErrorType = "ACTION"
	ErrTypePolicy         ErrorType = "POLICY"
	ErrTypeStrategy       ErrorType = "STRATEGY"
	ErrTypeLoadBalancer   ErrorType = "LOAD_BALANCER"
	ErrTypeDNS            ErrorType = "DNS"
	ErrTypeCache          ErrorType = "CACHE"
	ErrTypeValidation     ErrorType = "VALIDATION"
	ErrTypeAuth           ErrorType = "AUTH"
	ErrTypeFile           ErrorType = "FILE"
	ErrTypeConfig         ErrorType = "CONFIG"
	ErrTypeConfiguration  ErrorType = "CONFIGURATION"
	ErrTypeInitialization ErrorType = "INITIALIZATION"
	ErrTypeTimeout        ErrorType = "TIMEOUT"
	ErrTypeHTTP           ErrorType = "HTTP"
	ErrTypeAWS            ErrorType = "AWS"
	ErrTypeOperation      ErrorType = "OPERATION"
)

// ErrorCode 错误代码
type ErrorCode int

// String 返回错误代码字符串
func (e ErrorCode) String() string {
	return fmt.Sprintf("%d", int(e))
}

const (
	// 系统错误代码 (1000-1999)
	ErrCodeSystemFailure        ErrorCode = 1001
	ErrCodeSystemError          ErrorCode = 1002
	ErrCodeNetworkFailure       ErrorCode = 1003
	ErrCodeConfigInvalid        ErrorCode = 1004
	ErrCodeInvalidConfig        ErrorCode = 1005
	ErrCodeValidationFailed     ErrorCode = 1006
	ErrCodeInitializationFailed ErrorCode = 1007
	ErrCodeFileOperationFailed  ErrorCode = 1008
	ErrCodeFileReadFailed       ErrorCode = 1009
	ErrCodeFileWriteFailed      ErrorCode = 1010
	ErrCodeFilePathInvalid      ErrorCode = 1011
	ErrCodeConfigParseFailed    ErrorCode = 1012
	ErrCodeConfigValidationFailed ErrorCode = 1013
	ErrCodeConfigLoadFailed     ErrorCode = 1014

	// 配置验证具体错误代码 (1015-1099)
	ErrCodeConfigFieldRequired      ErrorCode = 1015
	ErrCodeConfigFieldInvalid       ErrorCode = 1016
	ErrCodeConfigProxyFileEmpty     ErrorCode = 1017
	ErrCodeConfigProxyFileNotFound  ErrorCode = 1018
	ErrCodeConfigPortInvalid        ErrorCode = 1019
	ErrCodeConfigPortReserved       ErrorCode = 1020
	ErrCodeConfigAuthTypeEmpty      ErrorCode = 1021
	ErrCodeConfigAuthIncomplete     ErrorCode = 1022
	ErrCodeConfigPasswordWeak       ErrorCode = 1023
	ErrCodeConfigDNSServerEmpty     ErrorCode = 1024
	ErrCodeConfigDNSServerInvalid   ErrorCode = 1025
	ErrCodeConfigDNSTimeoutInvalid  ErrorCode = 1026
	ErrCodeConfigCacheTypeEmpty     ErrorCode = 1027
	ErrCodeConfigCacheSizeInvalid   ErrorCode = 1028
	ErrCodeConfigCacheTTLInvalid    ErrorCode = 1029
	ErrCodeConfigMonitoringPortInvalid ErrorCode = 1030
	ErrCodeConfigMonitoringPathEmpty   ErrorCode = 1031
	ErrCodeConfigServiceDisabled    ErrorCode = 1032
	
	// 网络错误代码 (1100-1199)
	ErrCodeConnectionFailed     ErrorCode = 1101
	ErrCodeConnectionTimeout    ErrorCode = 1102
	ErrCodeDNSResolutionFailed  ErrorCode = 1103
	ErrCodeInvalidURL           ErrorCode = 1104
	
	// 代理错误代码 (2000-2999)
	ErrCodeNoProxyAvailable     ErrorCode = 2001
	ErrCodeProxyTimeout         ErrorCode = 2002
	ErrCodeProxyAuthFailed      ErrorCode = 2003
	ErrCodeProxyConnFailed      ErrorCode = 2004
	ErrCodeProxyBanned          ErrorCode = 2005
	ErrCodeProxyPoolExhausted   ErrorCode = 2006
	ErrCodeInvalidProxyURL      ErrorCode = 2007
	ErrCodeUnsupportedProtocol  ErrorCode = 2008
	ErrCodeTransportSwitchFailed ErrorCode = 2009
	ErrCodeProxyProtocolMismatch ErrorCode = 2010
	ErrCodeProxyProtocolUnsupported ErrorCode = 2011
	ErrCodeProxyUnavailable     ErrorCode = 2012
	ErrCodeInvalidInput         ErrorCode = 2013
	
	// 代理管理器相关错误代码 (2100-2199)
	ErrCodeProxyManagerNoValidProxies              ErrorCode = 2101
	ErrCodeProxyManagerQualityPoolNotInitialized   ErrorCode = 2102
	ErrCodeProxyManagerNoDomainProxies             ErrorCode = 2103
	ErrCodeProxyManagerNoAvailableProxies          ErrorCode = 2104
	ErrCodeProxyManagerAllProxiesBanned            ErrorCode = 2105
	ErrCodeProxyManagerIPAlreadyBlocked            ErrorCode = 2106
	ErrCodeProxyManagerInvalidDuration             ErrorCode = 2107
	ErrCodeInvalidResource                         ErrorCode = 2108
	ErrCodeProxyManagerTrustedIPCannotBan          ErrorCode = 2109
	ErrCodeInvalidScope                            ErrorCode = 2110
	ErrCodeTrustedResource                         ErrorCode = 2111
	ErrCodePermanentlyBlocked                      ErrorCode = 2112
	ErrCodeWatcherCreationFailed                   ErrorCode = 2113
	ErrCodeWatcherAddFailed                        ErrorCode = 2114
	ErrCodeNoSuitableProxy                         ErrorCode = 2115
	ErrCodeProxyManagerLoadFailed                  ErrorCode = 2116
	ErrCodeProxyNotFound                           ErrorCode = 2117
	
	// 触发器错误代码 (3000-3999)
	ErrCodeTriggerNotFound                         ErrorCode = 3001
	ErrCodeTriggerInvalidCondition                 ErrorCode = 3002
	ErrCodeTriggerExecutionFailed                  ErrorCode = 3003
	ErrCodeTriggerExecFailed    ErrorCode = 3004
	ErrCodeInvalidTriggerType   ErrorCode = 3005
	ErrCodeTriggerConditionFailed ErrorCode = 3006
	ErrCodeCreationFailed       ErrorCode = 3007
	
	// 动作错误代码 (4000-4999)
	ErrCodeActionParamMissing                      ErrorCode = 4001
	ErrCodeActionTypeUnknown                       ErrorCode = 4002
	ErrCodeActionManagerNotInitialized             ErrorCode = 4010
	ErrCodeActionNotFound       ErrorCode = 4003
	ErrCodeActionExecFailed     ErrorCode = 4004
	ErrCodeInvalidActionType    ErrorCode = 4005
	ErrCodeActionTimeout        ErrorCode = 4006
	ErrCodeActionQueueFull      ErrorCode = 4007
	ErrCodeMissingParameter     ErrorCode = 4008
	ErrCodeInvalidParameter     ErrorCode = 4009
	ErrCodeUnsupportedActionType ErrorCode = 4011
	ErrCodeActionDisabled       ErrorCode = 4012
	ErrCodeParameterValidationFailed ErrorCode = 4013
	ErrCodeServiceNotRunning    ErrorCode = 4014
	ErrCodeServiceAlreadyRunning ErrorCode = 4015
	ErrCodeQueueFull            ErrorCode = 4016
	ErrCodeResourceNotFound     ErrorCode = 4017
	ErrCodeInvalidConfiguration ErrorCode = 4018
	ErrCodeResourceAlreadyExists ErrorCode = 4019
	ErrCodeActionExecutionFailed ErrorCode = 4020
	ErrCodeConfigReloadFailed    ErrorCode = 4021
	
	// 策略错误代码 (5000-5999)
	ErrCodeStrategyNotFound     ErrorCode = 5001
	ErrCodeStrategyExecFailed   ErrorCode = 5002
	ErrCodeInvalidStrategyType  ErrorCode = 5003
	
	// 负载均衡相关错误代码 (5100-5199)
	ErrCodeLoadBalancerNoTargets        ErrorCode = 5101
	ErrCodeLoadBalancerNoHealthyTargets ErrorCode = 5102
	ErrCodeLoadBalancerConfigInvalid    ErrorCode = 5103
	
	// DNS错误代码 (6000-6999)
	ErrCodeDNSServerUnavailable     ErrorCode = 6001
	ErrCodeInvalidDNSServer         ErrorCode = 6002
	ErrCodeDNSQueryFailed           ErrorCode = 6003
	ErrCodeDNSConfigInvalid         ErrorCode = 6004
	ErrCodeDNSNoResult              ErrorCode = 6005
	ErrCodeDNSProtocolUnsupported   ErrorCode = 6006
	ErrCodeProxyConnectionFailed    ErrorCode = 6007
	ErrCodeProxyTypeUnsupported     ErrorCode = 6008
	ErrCodeProxyShutdownFailed      ErrorCode = 6009
	
	// 缓存错误代码 (7000-7999)
	ErrCodeCacheOperationFailed ErrorCode = 7001
	ErrCodeCacheKeyNotFound     ErrorCode = 7002
	ErrCodeCacheExpired         ErrorCode = 7003
	
	// 认证错误代码 (8000-8999)
	ErrCodeAuthenticationFailed ErrorCode = 8001
	ErrCodeAuthorizationFailed  ErrorCode = 8002
	ErrCodeInvalidCredentials   ErrorCode = 8003
	ErrCodeTokenExpired         ErrorCode = 8004
	
	// HTTP 相关错误代码 (9000-9099)
	ErrCodeHTTPRequestFailed       ErrorCode = 9001
	ErrCodeHTTPResponseInvalid     ErrorCode = 9002
	ErrCodeHTTPStatusError         ErrorCode = 9003
	ErrCodeHTTPHeaderInvalid       ErrorCode = 9004
	ErrCodeHTTPBodyInvalid         ErrorCode = 9005
	ErrCodeHTTPRequestCreateFailed ErrorCode = 9006
	
	// AWS 相关错误代码 (9100-9199)
	ErrCodeAWSCredentialsFailed  ErrorCode = 9101
	ErrCodeAWSRegionInvalid      ErrorCode = 9102
	ErrCodeAWSConfigLoadFailed   ErrorCode = 9103
	ErrCodeAWSEndpointInvalid    ErrorCode = 9104
	ErrCodeAWSAPICallFailed      ErrorCode = 9105
	ErrCodeAWSConfigInvalid      ErrorCode = 9106
	ErrCodeAWSEndpointNotAvailable ErrorCode = 9107
)

// FlexProxyError 统一错误类型
type FlexProxyError struct {
	Type      ErrorType   `json:"type"`
	Code      ErrorCode   `json:"code"`
	Message   string      `json:"message"`
	Details   string      `json:"details,omitempty"`
	Cause     error       `json:"-"` // 原始错误，不序列化
	Timestamp time.Time   `json:"timestamp"`
	File      string      `json:"file,omitempty"`
	Line      int         `json:"line,omitempty"`
	Function  string      `json:"function,omitempty"`
	TraceID   string      `json:"trace_id,omitempty"`
}

// Error 实现error接口
func (e *FlexProxyError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s:%d] %s: %s", e.Type, e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s:%d] %s", e.Type, e.Code, e.Message)
}

// Unwrap 支持错误链
func (e *FlexProxyError) Unwrap() error {
	return e.Cause
}

// Is 支持errors.Is
func (e *FlexProxyError) Is(target error) bool {
	if t, ok := target.(*FlexProxyError); ok {
		return e.Type == t.Type && e.Code == t.Code
	}
	return false
}

// NewError 创建新错误
func NewError(errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	pc, file, line, _ := runtime.Caller(1)
	func_name := runtime.FuncForPC(pc).Name()
	
	return &FlexProxyError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		File:      file,
		Line:      line,
		Function:  func_name,
	}
}

// NewErrorWithDetails 创建带详细信息的错误
func NewErrorWithDetails(errorType ErrorType, code ErrorCode, message, details string) *FlexProxyError {
	err := NewError(errorType, code, message)
	err.Details = details
	return err
}

// WrapError 包装错误
func WrapError(cause error, errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	err := NewError(errorType, code, message)
	err.Cause = cause
	return err
}

// WrapErrorWithDetails 包装现有错误并添加详细信息
func WrapErrorWithDetails(cause error, errorType ErrorType, code ErrorCode, message, details string) *FlexProxyError {
	err := NewErrorWithDetails(errorType, code, message, details)
	err.Cause = cause
	return err
}

// WithTraceID 添加追踪ID
func (e *FlexProxyError) WithTraceID(traceID string) *FlexProxyError {
	e.TraceID = traceID
	return e
}

// NewFlexProxyError 创建FlexProxyError的便捷函数（向后兼容）
func NewFlexProxyError(errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	return NewError(errorType, code, message)
}

// 预定义的常用错误
var (
	// 代理相关错误
	ErrNoProxyLeft = NewError(ErrTypeProxy, ErrCodeNoProxyAvailable, "代理池中没有可用代理")
	ErrProxyTimeout = NewError(ErrTypeProxy, ErrCodeProxyTimeout, "代理请求超时")
	ErrProxyAuthFailed = NewError(ErrTypeProxy, ErrCodeProxyAuthFailed, "代理认证失败")
	ErrProxyConnFailed = NewError(ErrTypeProxy, ErrCodeProxyConnFailed, "代理连接失败")
	ErrProxyBanned = NewError(ErrTypeProxy, ErrCodeProxyBanned, "代理已被封禁")
	ErrProxyPoolExhausted = NewError(ErrTypeProxy, ErrCodeProxyPoolExhausted, "代理池已耗尽")
	ErrInvalidProxyURL = NewError(ErrTypeProxy, ErrCodeInvalidProxyURL, "无效的代理URL")
	
	// 配置和验证错误
	ErrConfigInvalid = NewError(ErrTypeConfig, ErrCodeConfigInvalid, "配置无效")
	ErrValidationFailed = NewError(ErrTypeValidation, ErrCodeValidationFailed, "验证失败")
	ErrInitializationFailed = NewError(ErrTypeInitialization, ErrCodeInitializationFailed, "初始化失败")

	// 配置验证具体错误
	ErrConfigFieldRequired = NewError(ErrTypeValidation, ErrCodeConfigFieldRequired, "配置字段是必需的")
	ErrConfigFieldInvalid = NewError(ErrTypeValidation, ErrCodeConfigFieldInvalid, "配置字段值无效")
	ErrConfigProxyFileEmpty = NewError(ErrTypeValidation, ErrCodeConfigProxyFileEmpty, "代理文件路径不能为空")
	ErrConfigProxyFileNotFound = NewError(ErrTypeFile, ErrCodeConfigProxyFileNotFound, "代理文件不存在")
	ErrConfigPortInvalid = NewError(ErrTypeValidation, ErrCodeConfigPortInvalid, "端口配置无效")
	ErrConfigPortReserved = NewError(ErrTypeValidation, ErrCodeConfigPortReserved, "使用系统保留端口")
	ErrConfigAuthTypeEmpty = NewError(ErrTypeValidation, ErrCodeConfigAuthTypeEmpty, "认证类型不能为空")
	ErrConfigAuthIncomplete = NewError(ErrTypeValidation, ErrCodeConfigAuthIncomplete, "认证配置不完整")
	ErrConfigPasswordWeak = NewError(ErrTypeValidation, ErrCodeConfigPasswordWeak, "密码强度不足")
	ErrConfigDNSServerEmpty = NewError(ErrTypeValidation, ErrCodeConfigDNSServerEmpty, "DNS服务器地址不能为空")
	ErrConfigDNSServerInvalid = NewError(ErrTypeValidation, ErrCodeConfigDNSServerInvalid, "DNS服务器地址格式无效")
	ErrConfigDNSTimeoutInvalid = NewError(ErrTypeValidation, ErrCodeConfigDNSTimeoutInvalid, "DNS超时配置无效")
	ErrConfigCacheTypeEmpty = NewError(ErrTypeValidation, ErrCodeConfigCacheTypeEmpty, "缓存类型不能为空")
	ErrConfigCacheSizeInvalid = NewError(ErrTypeValidation, ErrCodeConfigCacheSizeInvalid, "缓存大小配置无效")
	ErrConfigCacheTTLInvalid = NewError(ErrTypeValidation, ErrCodeConfigCacheTTLInvalid, "缓存TTL配置无效")
	ErrConfigMonitoringPortInvalid = NewError(ErrTypeValidation, ErrCodeConfigMonitoringPortInvalid, "监控端口配置无效")
	ErrConfigMonitoringPathEmpty = NewError(ErrTypeValidation, ErrCodeConfigMonitoringPathEmpty, "监控路径不能为空")
	ErrConfigServiceDisabled = NewError(ErrTypeValidation, ErrCodeConfigServiceDisabled, "代理服务未启用")
	
	// 网络相关错误
	ErrConnectionFailed = NewError(ErrTypeNetwork, ErrCodeConnectionFailed, "连接失败")
	ErrConnectionTimeout = NewError(ErrTypeNetwork, ErrCodeConnectionTimeout, "连接超时")
	ErrDNSResolutionFailed = NewError(ErrTypeNetwork, ErrCodeDNSResolutionFailed, "DNS解析失败")
	ErrInvalidURL = NewError(ErrTypeNetwork, ErrCodeInvalidURL, "无效的URL")
	
	// 触发器相关错误
	ErrTriggerNotFound = NewError(ErrTypeTrigger, ErrCodeTriggerNotFound, "触发器未找到")
	ErrTriggerExecFailed = NewError(ErrTypeTrigger, ErrCodeTriggerExecFailed, "触发器执行失败")
	ErrInvalidTriggerType = NewError(ErrTypeTrigger, ErrCodeInvalidTriggerType, "无效的触发器类型")
	
	// 动作相关错误
	ErrActionNotFound = NewError(ErrTypeAction, ErrCodeActionNotFound, "动作未找到")
	ErrActionExecFailed = NewError(ErrTypeAction, ErrCodeActionExecFailed, "动作执行失败")
	ErrInvalidActionType = NewError(ErrTypeAction, ErrCodeInvalidActionType, "无效的动作类型")
	ErrActionTimeout = NewError(ErrTypeAction, ErrCodeActionTimeout, "动作执行超时")
	ErrActionQueueFull = NewError(ErrTypeAction, ErrCodeActionQueueFull, "动作队列已满")
	ErrMissingParameter = NewError(ErrTypeAction, ErrCodeMissingParameter, "缺少必需参数")
	ErrInvalidParameter = NewError(ErrTypeAction, ErrCodeInvalidParameter, "无效参数")
	
	// 策略相关错误
	ErrStrategyNotFound = NewError(ErrTypeStrategy, ErrCodeStrategyNotFound, "策略未找到")
	ErrStrategyExecFailed = NewError(ErrTypeStrategy, ErrCodeStrategyExecFailed, "策略执行失败")
	ErrInvalidStrategyType = NewError(ErrTypeStrategy, ErrCodeInvalidStrategyType, "无效的策略类型")
	
	// DNS相关错误
	ErrDNSServerUnavailable = NewError(ErrTypeDNS, ErrCodeDNSServerUnavailable, "DNS服务器不可用")
	ErrInvalidDNSServer = NewError(ErrTypeDNS, ErrCodeInvalidDNSServer, "无效的DNS服务器")
	ErrDNSQueryFailed = NewError(ErrTypeDNS, ErrCodeDNSQueryFailed, "DNS查询失败")
	
	// 缓存相关错误
	ErrCacheOperationFailed = NewError(ErrTypeCache, ErrCodeCacheOperationFailed, "缓存操作失败")
	ErrCacheKeyNotFound = NewError(ErrTypeCache, ErrCodeCacheKeyNotFound, "缓存键未找到")
	ErrCacheExpired = NewError(ErrTypeCache, ErrCodeCacheExpired, "缓存条目已过期")
	
	// 认证相关错误
	ErrAuthenticationFailed = NewError(ErrTypeAuth, ErrCodeAuthenticationFailed, "认证失败")
	ErrAuthorizationFailed = NewError(ErrTypeAuth, ErrCodeAuthorizationFailed, "授权失败")
	ErrInvalidCredentials = NewError(ErrTypeAuth, ErrCodeInvalidCredentials, "无效的凭据")
	ErrTokenExpired = NewError(ErrTypeAuth, ErrCodeTokenExpired, "令牌已过期")
	
	// HTTP相关错误
	ErrHTTPRequestFailed       = NewError(ErrTypeHTTP, ErrCodeHTTPRequestFailed, "HTTP请求失败")
	ErrHTTPResponseInvalid     = NewError(ErrTypeHTTP, ErrCodeHTTPResponseInvalid, "HTTP响应无效")
	ErrHTTPStatusError         = NewError(ErrTypeHTTP, ErrCodeHTTPStatusError, "HTTP状态错误")
	ErrHTTPHeaderInvalid       = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "HTTP头部无效")
	ErrHTTPBodyInvalid         = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "HTTP主体无效")
	ErrHTTPRequestCreateFailed = NewError(ErrTypeHTTP, ErrCodeHTTPRequestCreateFailed, "HTTP请求创建失败")
	
	// AWS相关错误
	ErrAWSCredentialsFailed = NewError(ErrTypeAWS, ErrCodeAWSCredentialsFailed, "AWS凭据失败")
	ErrAWSRegionInvalid = NewError(ErrTypeAWS, ErrCodeAWSRegionInvalid, "AWS区域无效")
	ErrAWSConfigLoadFailed = NewError(ErrTypeAWS, ErrCodeAWSConfigLoadFailed, "AWS配置加载失败")
	ErrAWSEndpointInvalid = NewError(ErrTypeAWS, ErrCodeAWSEndpointInvalid, "AWS端点无效")
	ErrAWSAPICallFailed = NewError(ErrTypeAWS, ErrCodeAWSAPICallFailed, "AWS API调用失败")
	ErrAWSConfigInvalid = NewError(ErrTypeAWS, ErrCodeAWSConfigInvalid, "AWS配置无效")
	ErrAWSEndpointNotAvailable = NewError(ErrTypeAWS, ErrCodeAWSEndpointNotAvailable, "AWS端点不可用")
)
