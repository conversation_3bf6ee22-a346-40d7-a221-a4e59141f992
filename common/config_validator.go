package common

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/mubeng/mubeng/common/errors"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	validator *validator.Validate
}

// NewConfigValidator 创建新的配置验证器
func NewConfigValidator() *ConfigValidator {
	v := validator.New()
	
	// 注册自定义验证规则
	v.RegisterValidation("duration_or_reboot", validateDurationOrReboot)
	v.RegisterValidation("action_type", validateActionType)
	v.RegisterValidation("trigger_type", validateTriggerType)
	
	return &ConfigValidator{
		validator: v,
	}
}

// ValidateConfig 验证配置结构
func (cv *ConfigValidator) ValidateConfig(config *Config) error {
	if config == nil {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			errors.ErrConfigFieldRequired.Message, "配置不能为空")
	}

	// 执行结构验证
	err := cv.validator.Struct(config)
	if err != nil {
		return cv.formatValidationError(err)
	}

	// 执行业务逻辑验证
	if err := cv.validateBusinessLogic(config); err != nil {
		return err
	}

	// 执行深度验证
	if err := cv.validateDeepLogic(config); err != nil {
		return err
	}

	return nil
}

// validateBusinessLogic 执行业务逻辑验证
func (cv *ConfigValidator) validateBusinessLogic(config *Config) error {
	// 验证动作序列引用
	for eventName, event := range config.Events {
		for _, match := range event.Matches {
			for _, action := range match.Actions {
				if action.Type == "" {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
						"事件中的动作类型不能为空", fmt.Sprintf("事件名称: %v", eventName))
				}
			}
		}
	}

	// 验证触发器条件
	for eventIndex, event := range config.Events {
		// 事件必须至少有一个条件、匹配规则或条件动作
		if len(event.Conditions) == 0 && len(event.Matches) == 0 && len(event.ConditionalActions) == 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				"事件必须至少有一个条件、匹配规则或条件动作", fmt.Sprintf("事件索引: %d, 事件名称: %s", eventIndex, event.Name))
		}
	}

	return nil
}

// validateDeepLogic 执行深度业务逻辑验证
func (cv *ConfigValidator) validateDeepLogic(config *Config) error {
	// 验证代理文件路径
	if err := cv.validateProxyFile(config); err != nil {
		return err
	}

	// 验证端口配置
	if err := cv.validatePortConfiguration(config); err != nil {
		return err
	}

	// 验证认证配置
	if err := cv.validateAuthConfiguration(config); err != nil {
		return err
	}

	// 验证DNS配置
	if err := cv.validateDNSConfiguration(config); err != nil {
		return err
	}

	// 验证缓存配置
	if err := cv.validateCacheConfiguration(config); err != nil {
		return err
	}

	// 验证监控配置
	if err := cv.validateMonitoringConfiguration(config); err != nil {
		return err
	}

	return nil
}

// validateProxyFile 验证代理文件配置
func (cv *ConfigValidator) validateProxyFile(config *Config) error {
	if config.Global.ProxyFile == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigProxyFileEmpty,
			errors.ErrConfigProxyFileEmpty.Message, "请在 global.proxy_file 中指定有效的代理文件路径")
	}

	// 检查文件是否存在（如果是相对路径或绝对路径）
	if config.Global.ProxyFile != "" {
		// 这里不直接检查文件存在性，因为可能在运行时才创建
		// 但可以检查路径格式的合理性
		if len(config.Global.ProxyFile) > 255 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"代理文件路径过长", "路径长度不能超过255个字符")
		}
	}

	return nil
}

// validatePortConfiguration 验证端口配置
func (cv *ConfigValidator) validatePortConfiguration(config *Config) error {
	if config.Server != nil {
		if config.Server.Port < 1 || config.Server.Port > 65535 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigPortInvalid,
				errors.ErrConfigPortInvalid.Message, fmt.Sprintf("端口必须在1-65535范围内，当前值: %d", config.Server.Port))
		}

		// 检查是否使用了系统保留端口
		if config.Server.Port < 1024 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigPortReserved,
				errors.ErrConfigPortReserved.Message, fmt.Sprintf("端口 %d 是系统保留端口，建议使用1024以上的端口", config.Server.Port))
		}
	}

	return nil
}

// validateAuthConfiguration 验证认证配置
func (cv *ConfigValidator) validateAuthConfiguration(config *Config) error {
	if config.Security != nil && config.Security.Auth != nil {
		auth := config.Security.Auth

		// 验证认证类型
		if auth.Type == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigAuthTypeEmpty,
				errors.ErrConfigAuthTypeEmpty.Message, "请在 security.auth.type 中指定认证类型 (none, basic, bearer, apikey)")
		}

		// 验证token过期时间格式
		if auth.TokenExpiry != "" {
			// 这里可以添加时间格式验证逻辑
			if len(auth.TokenExpiry) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					"Token过期时间格式无效", "请检查 security.auth.token_expiry 的格式")
			}
		}
	}

	return nil
}

// validateDNSConfiguration 验证DNS配置
func (cv *ConfigValidator) validateDNSConfiguration(config *Config) error {
	if config.DNSService != nil {
		dns := config.DNSService

		// 验证DNS服务器配置
		if dns.Servers != nil {
			for name, server := range dns.Servers {
				if server == "" {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigDNSServerEmpty,
						errors.ErrConfigDNSServerEmpty.Message, fmt.Sprintf("dns_service.servers['%s'] 不能为空", name))
				}

				// 简单的IP地址格式检查
				if !cv.isValidIPOrDomain(server) {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigDNSServerInvalid,
						errors.ErrConfigDNSServerInvalid.Message, fmt.Sprintf("dns_service.servers['%s'] = '%s' 不是有效的IP地址或域名", name, server))
				}
			}
		}

		// 验证超时配置格式
		if dns.Timeout != "" {
			// 这里可以添加时间格式验证逻辑
			if len(dns.Timeout) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigDNSTimeoutInvalid,
					errors.ErrConfigDNSTimeoutInvalid.Message, "请检查 dns_service.timeout 的格式")
			}
		}

		// 验证重试次数
		if dns.Retries < 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"DNS重试次数无效", "dns_service.retries 不能为负数")
		}
	}

	return nil
}

// validateCacheConfiguration 验证缓存配置
func (cv *ConfigValidator) validateCacheConfiguration(config *Config) error {
	if config.Cache != nil {
		cache := config.Cache

		// 验证缓存类型
		if cache.Type == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigCacheTypeEmpty,
				errors.ErrConfigCacheTypeEmpty.Message, "请在 cache.type 中指定缓存类型 (memory, redis, file)")
		}

		// 验证缓存大小
		if cache.Size < 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigCacheSizeInvalid,
				errors.ErrConfigCacheSizeInvalid.Message, "cache.size 不能为负数")
		}

		// 验证TTL配置格式
		if cache.TTL != "" {
			// 这里可以添加时间格式验证逻辑
			if len(cache.TTL) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigCacheTTLInvalid,
					errors.ErrConfigCacheTTLInvalid.Message, "请检查 cache.ttl 的格式")
			}
		}

		// 验证清理间隔格式
		if cache.CleanupInterval != "" {
			// 这里可以添加时间格式验证逻辑
			if len(cache.CleanupInterval) == 0 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					"缓存清理间隔格式无效", "请检查 cache.cleanup_interval 的格式")
			}
		}
	}

	return nil
}

// validateMonitoringConfiguration 验证监控配置
func (cv *ConfigValidator) validateMonitoringConfiguration(config *Config) error {
	if config.Monitoring != nil {
		monitoring := config.Monitoring

		// 验证监控端口
		if monitoring.Port < 1 || monitoring.Port > 65535 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigMonitoringPortInvalid,
				errors.ErrConfigMonitoringPortInvalid.Message, fmt.Sprintf("monitoring.port 必须在1-65535范围内，当前值: %d", monitoring.Port))
		}

		// 验证监控路径
		if monitoring.Path == "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigMonitoringPathEmpty,
				errors.ErrConfigMonitoringPathEmpty.Message, "请在 monitoring.path 中指定监控路径")
		}
	}

	return nil
}

// isValidIPOrDomain 检查是否为有效的IP地址或域名
func (cv *ConfigValidator) isValidIPOrDomain(addr string) bool {
	// 简单的格式检查，实际项目中可以使用更严格的验证
	if addr == "" {
		return false
	}

	// 检查是否包含非法字符
	for _, char := range addr {
		if !((char >= 'a' && char <= 'z') ||
			 (char >= 'A' && char <= 'Z') ||
			 (char >= '0' && char <= '9') ||
			 char == '.' || char == '-' || char == ':') {
			return false
		}
	}

	return true
}

// formatValidationError 格式化验证错误信息
func (cv *ConfigValidator) formatValidationError(err error) error {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var errorMessages []string
		
		for _, fieldError := range validationErrors {
			fieldName := cv.getFieldName(fieldError)
			errorMsg := cv.getErrorMessage(fieldError)
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", fieldName, errorMsg))
		}
		
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigValidationFailed, "配置验证失败", strings.Join(errorMessages, "\n"))
	}

	return errors.WrapError(err, errors.ErrTypeValidation, errors.ErrCodeConfigValidationFailed, "配置验证失败")
}

// getFieldName 获取字段名称
func (cv *ConfigValidator) getFieldName(fieldError validator.FieldError) string {
	namespace := fieldError.Namespace()
	// 移除结构体名称前缀
	if idx := strings.Index(namespace, "."); idx != -1 {
		return namespace[idx+1:]
	}
	return namespace
}

// getErrorMessage 获取错误信息
func (cv *ConfigValidator) getErrorMessage(fieldError validator.FieldError) string {
	switch fieldError.Tag() {
	case "required":
		return "此字段是必需的"
	case "ip":
		return "必须是有效的IP地址"
	case "fqdn":
		return "必须是有效的域名"
	case "min":
		return fmt.Sprintf("最小值为 %s", fieldError.Param())
	case "max":
		return fmt.Sprintf("最大值为 %s", fieldError.Param())
	case "oneof":
		return fmt.Sprintf("必须是以下值之一: %s", fieldError.Param())
	case "dive":
		return "数组或切片元素验证失败"
	default:
		return fmt.Sprintf("验证失败: %s", fieldError.Tag())
	}
}

// 自定义验证函数

// validateDurationOrReboot 验证duration字段（可以是int或"reboot"字符串）
func validateDurationOrReboot(fl validator.FieldLevel) bool {
	value := fl.Field()
	
	switch value.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return value.Int() >= 0
	case reflect.String:
		return value.String() == "reboot"
	case reflect.Interface:
		// 处理interface{}类型
		if value.IsNil() {
			return false
		}
		actualValue := value.Elem()
		switch actualValue.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return actualValue.Int() >= 0
		case reflect.String:
			return actualValue.String() == "reboot"
		}
	}
	
	return false
}

// validateActionType 验证动作类型
func validateActionType(fl validator.FieldLevel) bool {
	actionType := fl.Field().String()
	// 支持所有Executor实现的动作类型
	validTypes := []string{
		// 基础Executor动作类型
		"log", "banip", "ban_domain", "block_request",
		"modify_request", "modify_response", "cache_response", "script",
		// 从Action接口转换的Executor动作类型
		"retry_same", "retry", "banipdomain", "save_to_pool",
		"cache", "request_url", "null_response", "bypass_proxy",
	}

	for _, validType := range validTypes {
		if actionType == validType {
			return true
		}
	}
	return false
}

// validateTriggerType 验证触发器类型
func validateTriggerType(fl validator.FieldLevel) bool {
	triggerType := fl.Field().String()
	validTypes := []string{
		"status", "body", "max_request_time", "conn_time_out",
		"min_request_time", "url", "domain", "combined",
		"custom", "request_body", "request_header", "response_header",
	}
	
	for _, validType := range validTypes {
		if triggerType == validType {
			return true
		}
	}
	return false
}